using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Common;
using System.Collections.Generic;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IOrderService
{
    Task<ServiceResult<DropdownDataDto>> GetDropdownDataAsync();
    Task<ServiceResult<OrderSummaryDto>> CreateOrderAsync(OrderNewDto orderDto);
    Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsAsync(int orderId);
    Task<ServiceResult> UploadAttachmentAsync(int orderId, byte[] fileData, string fileName);
    Task<ServiceResult<byte[]>> DownloadAttachmentsZipAsync(int orderId);
    
    // DirectManager specific methods
    Task<ServiceResult<List<OrderSummaryDto>>> GetPendingOrdersForDirectMangerAsync();
    Task<ServiceResult> ConfirmOrderByDirectManagerAsync(int orderId, string? userName);
    Task<ServiceResult> RejectOrderByDirectManagerAsync(int orderId, string reason, string? userName);
    
    // AssistantManager specific methods
    Task<ServiceResult<List<OrderSummaryDto>>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId);
    Task<ServiceResult> ConfirmOrderByAssistantManagerAsync(int orderId, string? userName);
    Task<ServiceResult> ReturnOrderToDirectManagerAsync(int orderId, string reason, string? userName);
    Task<ServiceResult> RejectOrderByAssistantManagerAsync(int orderId, string reason, string? userName);

    // Print page methods
    Task<ServiceResult<List<OrderPrintListItemDto>>> GetPrintableOrdersAsync(string searchTerm, string filter);
    Task<ServiceResult<OrderPrintDetailsDto>> GetOrderPrintDetailsAsync(int orderId);
    Task<ServiceResult<byte[]>> DownloadOrderAttachmentsZipAsync(int orderId);
    Task<ServiceResult<byte[]>> GenerateOrderPdfAsync(int orderId);

    Task<ServiceResult> ReturnOrderAsync(int orderId, string rejectReason, string username);
    Task<ServiceResult> MarkOrderNeedsActionAsync(int orderId, string actionRequired, string username);
}
