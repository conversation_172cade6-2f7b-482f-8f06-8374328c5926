using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.IO;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.IO.Compression;
using DocumentFormat.OpenXml.Drawing;
using System.Data;
using abozyad.Helpers;
using System.Linq;
using DocumentFormat.OpenXml.Office.Word;





namespace abozyad
{



    // تقوم هذه الدالة بمعالجة تحميل الصفحة عند استدعائها:
    // 1. التحقق من صلاحية المستخدم المخزنة في الجلسة:
    //    - إذا كانت الصلاحيات غير موجودة أو لا تساوي "منسق الموارد البشرية"، يتم إعادة توجيه المستخدم إلى صفحة "AccessDenied.aspx".
    // 2. إذا كان يتم تحميل الصفحة لأول مرة (وليس بسبب PostBack)، يتم استدعاء الدالة PopulateOrderNumbers() لتحميل أرقام الطلبات.
    public partial class WebForm5 : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            Session["UserPermission"] = "منسق الموارد البشرية";
            if (Session["UserPermission"] == null || Session["UserPermission"].ToString() != "منسق الموارد البشرية")
            {
                // Redirect to an access denied page or display an error message
                Response.Redirect("AccessDenied.aspx");
            }



            if (!IsPostBack)
            {
                PopulateOrderNumbers();
                PopulateRestorableOrders();  // للطلبات القابلة للاستعادة
                PopulateRestorableOrders(searchTerm: "", filter: "today");
            }
        }


        // تقوم هذه الدالة بمعالجة تغيير القيمة المختارة في القائمة المنسدلة لأرقام الطلبات:
        // 1. إذا كانت القيمة المختارة ليست "0"، يتم استدعاء الدالة LoadOrderDetails لتحميل تفاصيل الطلب بناءً على رقم الطلب المختار.
        protected void ddlOrderNumbers_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (ddlOrderNumbers.SelectedValue != "0")
                {
                    LoadOrderDetails(ddlOrderNumbers.SelectedValue);
                    ResetButtonsState();
                    string message = "";

                    // معالجة التحذيرات
                    string orderStatus = LabelOrderStatus.Text.Trim();
                    string rejectionReason = LabelCancellationReason.Text.Trim();

                    // التحقق من حالة "يتطلب إجراءات"
                    if (orderStatus == "يتطلب إجراءات")
                    {
                        HandleActionRequired(ref message);
                    }

                    if (orderStatus == "أُعيد بواسطة المدير")
                    {
                        HandleManagerRejection(ref message, rejectionReason);
                    }

                    bool hasSupervisorRejection = CheckForSupervisorRejections(ddlOrderNumbers.SelectedValue);
                    if (hasSupervisorRejection)
                    {
                        HandleSupervisorRejection(ref message, ddlOrderNumbers.SelectedValue);
                    }
                    else
                    {
                        RejectionAlertPanel.Visible = false;
                    }

                    // معالجة المسار التلقائي
                    string orderType = LabelOrderType.Text.Trim();
                    string nationality = ProcessNationality(LabelNationality.Text.Trim());
                    string job = ProcessJob(LabelJobTitle.Text.Trim());

                    LogOrderDetails(orderType, nationality, job);

                    // تحديث معلومات المسار التلقائي
                    UpdateAutoPathInfo(orderType, nationality, job, ref message);

                    // تحديث واجهة المستخدم
                    AutoPathInfoPanel.InnerHtml = message;
                    AutoPathInfoPanel.Visible = true;
                }
            }
            catch (Exception ex)
            {
                HandleError(ex);
            }
        }

        private void HandleManagerRejection(ref string message, string rejectionReason)
        {
            string rejectionSource = "مدير الموارد البشرية";
            message += $@"<div class='alert alert-warning text-right' role='alert'>
        <h5 class='alert-heading'>⚠️ تنبيه: هذا الطلب تمت إعادته من مدير الموارد البشرية</h5>
        <hr><p class='mb-0'>سبب الإعادة: {rejectionReason}</p></div>";

            ShowRejectionAlert("تنبيه", rejectionSource, rejectionReason);
        }

        private void HandleSupervisorRejection(ref string message, string orderNumber)
        {
            string rejectionDetails = GetSupervisorRejectionDetails(orderNumber);
            message += $@"<div class='alert alert-danger text-right' role='alert'>
        <h5 class='alert-heading'>⚠️ تحذير شديد!</h5><hr>
        <strong>هذا الطلب تمت إعادته من قبل المشرفين:</strong>
        <ul class='mt-2 list-unstyled'>{rejectionDetails}</ul>
        <hr><small class='text-danger'>يرجى مراجعة سبب الإعادة قبل تحويل الطلب للمدير</small></div>";

            ShowRejectionAlert("تحذير شديد", "المشرفين", rejectionDetails);
        }

        private void UpdateAutoPathInfo(string orderType, string nationality, string job, ref string message)
        {
            using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
            {
                string query = @"
            SELECT المشرفين as SupervisorsList
            FROM AutoRouting 
            WHERE نوع_الطلب = @OrderType 
            AND الجنسية = @Nationality
            AND حالة_المسار = 1
            AND (
                الوظيفة = 'ALL' 
                OR الوظيفة = @Job
                OR (الوظيفة = 'ALL_EXCEPT_OTHER' AND @Job NOT LIKE 'أخرى - Other:%')
            )";

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@OrderType", orderType);
                    cmd.Parameters.AddWithValue("@Nationality", nationality);
                    cmd.Parameters.AddWithValue("@Job", job);

                    conn.Open();
                    var result = cmd.ExecuteScalar();

                    if (result != null && result != DBNull.Value)
                    {
                        HandleAvailableAutoPath(result.ToString(), ref message);
                    }
                    else
                    {
                        HandleNoAutoPath(ref message);
                    }
                }
            }
        }

        private void HandleActionRequired(ref string message)
        {
            string actionDetails = GetActionRequiredDetails();
            message += BuildActionRequiredMessage(actionDetails);
            ShowActionRequiredAlert(actionDetails);
        }

        private string GetActionRequiredDetails()

        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                string query = "SELECT [ملاحظات المشرفين] FROM OrdersTable WHERE [رقم الطلب] = @OrderNumber";
                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", ddlOrderNumbers.SelectedValue);
                    conn.Open();
                    return cmd.ExecuteScalar()?.ToString() ?? "";
                }
            }
        }
        private string BuildActionRequiredMessage(string actionDetails)
        {
            return $@"<div class='alert alert-warning text-right' role='alert'>
        <h5 class='alert-heading'>⚠️ تنبيه: هذا الطلب يتطلب إجراءات</h5>
        <hr>
        <p>التفاصيل: {actionDetails}</p>
        <hr>
        <p class='mb-0'>نرجو مراجعة الإجراءات المطلوبة</p>
    </div>";
        }
        private void ShowActionRequiredAlert(string actionDetails)
        {
            string script = $@"
        Swal.fire({{
            title: 'تنبيه',
            html: 'هذا الطلب يتطلب إجراءات<br>التفاصيل: {actionDetails}',
            icon: 'warning',
            confirmButtonText: 'حسناً'
        }});";
            ScriptManager.RegisterStartupScript(this, GetType(), "ActionAlert", script, true);
        }
        private void HandleAvailableAutoPath(string supervisors, ref string message)
        {
            btnAutoPath.CssClass = "auto-path-btn available-path";
            btnAutoPath.Enabled = true;

            message += BuildAutoPathMessage(supervisors);
        }

        private void HandleNoAutoPath(ref string message)
        {
            btnAutoPath.CssClass = "auto-path-btn disabled-path";
            btnAutoPath.Enabled = false;

            message += @"<div class='alert alert-secondary text-right' role='alert'>
        <h5 class='alert-heading'>ℹ️ لا يوجد مسار تلقائي</h5>
        <p class='mb-0'>هذا الطلب يحتاج إلى توجيه يدوي</p></div>";
        }

        private string BuildAutoPathMessage(string supervisors)
        {
            var supervisorList = supervisors.Split(';');
            var message = @"<div class='alert alert-info text-right' role='alert'>
        <h5 class='alert-heading'>✨ متوفر مسار تلقائي لهذا الطلب</h5>
        <hr><p class='mb-0'>سيتم توجيه الطلب إلى:</p>
        <ul class='list-unstyled mt-2'>";

            foreach (string supervisor in supervisorList)
            {
                message += $"<li><i class='fas fa-check-circle text-success'></i> {supervisor.Trim()}</li>";
            }

            message += @"</ul><hr>
        <small class='text-muted'>يمكنك النقر على زر 'التوجيه التلقائي' لتطبيق هذا المسار</small>
        </div>";

            return message;
        }

        private string ProcessNationality(string nationality)
        {
            if (string.IsNullOrWhiteSpace(nationality))
                return string.Empty;

            nationality = nationality.Trim();

            // معالجة الجنسية السعودية بمختلف صيغها
            string[] saudiVariations = new[] {
        "سعودي",
        "سعودية",
        "السعودية",
        "سعودي - Saudi",
        "Saudi"
    };

            if (saudiVariations.Any(v => nationality.Contains(v)))
            {
                return "سعودي - Saudi";
            }

            // معالجة جميع الجنسيات الأخرى باستثناء "غير سعودي - Non-Saudi"
            if (!nationality.Contains("غير سعودي - Non-Saudi") &&
                !nationality.Contains("Other") &&
                !saudiVariations.Any(v => nationality.Contains(v)))
            {
                return "غير سعودي - Non-Saudi";
            }

            return nationality;
        }

        private string ProcessJob(string job)
        {
            return job.StartsWith("أخرى - Other:") ? "أخرى" : job;
        }

        private void ShowRejectionAlert(string title, string source, string reason)
        {
            string script = $@"
        Swal.fire({{
            title: '{title}',
            html: 'هذا الطلب تمت إعادته من {source}<br>{reason}',
            icon: 'warning',
            confirmButtonText: 'حسناً'
        }});";
            ScriptManager.RegisterStartupScript(this, GetType(), "RejectionAlert", script, true);
        }

        private void LogOrderDetails(string orderType, string nationality, string job)
        {
            System.Diagnostics.Debug.WriteLine($"=== بيانات الطلب ===");
            System.Diagnostics.Debug.WriteLine($"نوع الطلب: {orderType}");
            System.Diagnostics.Debug.WriteLine($"الجنسية: {nationality}");
            System.Diagnostics.Debug.WriteLine($"الوظيفة: {job}");
        }

        private void HandleError(Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"=== خطأ ===");
            System.Diagnostics.Debug.WriteLine($"Error: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"StackTrace: {ex.StackTrace}");

            LabelError.Visible = true;
            LabelError.Text = ex.Message;
            ShowAlert("حدث خطأ أثناء تحميل معلومات المسار: " + ex.Message, "error");
        }

        private bool CheckForSupervisorRejections(string orderNumber)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT COUNT(*) FROM ordersTable WHERE [رقم الطلب] = @OrderNumber AND (
            [مشرف خدمات الموظفين] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة تخطيط الموارد البشرية] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة تقنية المعلومات] LIKE N'%تم الإعادة%' OR
            [مشرف مراقبة الدوام] LIKE N'%تم الإعادة%' OR
            [مشرف السجلات الطبية] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة الرواتب والاستحقاقات] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة القانونية والالتزام] LIKE N'%تم الإعادة%' OR
            [مشرف خدمات الموارد البشرية] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة الإسكان] LIKE N'%تم الإعادة%' OR
            [مشرف قسم الملفات] LIKE N'%تم الإعادة%' OR
            [مشرف العيادات الخارجية] LIKE N'%تم الإعادة%' OR
            [مشرف التأمينات الاجتماعية] LIKE N'%تم الإعادة%' OR
            [مشرف وحدة مراقبة المخزون] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة تنمية الإيرادات] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة الأمن و السلامة] LIKE N'%تم الإعادة%' OR
            [مشرف الطب الاتصالي] LIKE N'%تم الإعادة%')";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    con.Open();
                    int count = (int)cmd.ExecuteScalar();
                    return count > 0;
                }
            }
        }

        private string GetSupervisorRejectionDetails(string orderNumber)
        {
            string details = "";
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string[] supervisorColumns = {
            "مشرف خدمات الموظفين",
            "مشرف إدارة تخطيط الموارد البشرية",
            "مشرف إدارة تقنية المعلومات",
            "مشرف مراقبة الدوام",
            "مشرف السجلات الطبية",
            "مشرف إدارة الرواتب والاستحقاقات",
            "مشرف إدارة القانونية والالتزام",
            "مشرف خدمات الموارد البشرية",
            "مشرف إدارة الإسكان",
            "مشرف قسم الملفات",
            "مشرف العيادات الخارجية",
            "مشرف التأمينات الاجتماعية",
            "مشرف وحدة مراقبة المخزون",
            "مشرف إدارة تنمية الإيرادات",
            "مشرف إدارة الأمن و السلامة",
            "مشرف الطب الاتصالي"
        };

                con.Open();
                foreach (string column in supervisorColumns)
                {
                    string query = $"SELECT [{column}] FROM ordersTable WHERE [رقم الطلب] = @OrderNumber AND [{column}] LIKE N'%تم الإعادة%'";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                        var result = cmd.ExecuteScalar()?.ToString();
                        if (!string.IsNullOrEmpty(result))
                        {
                            details += $"<li class='text-danger mb-2'><i class='fas fa-exclamation-triangle'></i> {result}</li>";
                        }
                    }
                }
            }
            return details;
        }

        private bool IsJobTitleMatching(string requestJobTitle, string routeJobTitle)
        {
            if (routeJobTitle == "ALL")
            {
                return true;
            }

            if (routeJobTitle == "ALL_EXCEPT_OTHER")
            {
                return !requestJobTitle.StartsWith("أخرى - Other:");
            }

            return requestJobTitle == routeJobTitle;
        }

        // تقوم هذه الدالة بتحميل تفاصيل الطلب بناءً على رقم الطلب الممرر كمعامل:
        // 1. الاتصال بقاعدة البيانات باستخدام SqlConnection.
        // 2. تنفيذ استعلام SQL لاسترجاع تفاصيل الطلبات المرتبطة برقم الطلب المحدد.
        // 3. تعيين القيم المسترجعة في الحقول المناسبة (مثل رقم الطلب، حالة الطلب، تفاصيل المشرفين...إلخ).
        // 4. إذا تم العثور على الطلب، يتم عرض التفاصيل في OrderDetailsPanel.
        // 5. إذا لم يتم العثور على الطلب، يتم إخفاء OrderDetailsPanel.
        private void LoadOrderDetails(string orderNumber, string source = "main")
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT [رقم الطلب], [تاريخ الطلب], [حالة الطلب], [نوع الطلب], [اسم الموظف],
                [القسم], [تفاصيل مقدم الطلب], [الوظيفة], [رقم الموظف], [السجل المدني], 
                [الجنسية], [رقم الجوال], [نوع التوظيف], [المؤهل], 
                [تم التأكيد/الإلغاء من مدير القسم], [تم التأكيد/الإلغاء من قبل المشرف], 
                [تم التحويل/الإلغاء من قبل المنسق], [سبب الإلغاء/الإعادة], [تفاصيل المنسق], 
                [مشرف خدمات الموظفين], [مشرف إدارة تخطيط الموارد البشرية], 
                [مشرف إدارة تقنية المعلومات], [مشرف مراقبة الدوام], [مشرف السجلات الطبية], 
                [مشرف إدارة الرواتب والاستحقاقات], [مشرف إدارة القانونية والالتزام], 
                [مشرف خدمات الموارد البشرية], [مشرف إدارة الإسكان], [مشرف قسم الملفات], 
                [مشرف العيادات الخارجية], [مشرف التأمينات الاجتماعية], [مشرف وحدة مراقبة المخزون], 
                [مشرف إدارة تنمية الإيرادات], [مشرف إدارة الأمن و السلامة], [مشرف الطب الاتصالي], 
                [مدير الموارد البشرية], [نوع التحويل], [ملاحظات المشرفين], 
                [ملف1], [ملف2], [ملف3], [ملف4]  
                        FROM OrdersTable WHERE [رقم الطلب] = @OrderNumber";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    con.Open();
                    SqlDataReader reader = cmd.ExecuteReader();

                    if (reader.Read())
                    {
                        SetLabelVisibilityAndText(LabelOrderNumber, reader["رقم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderDate, reader["تاريخ الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderStatus, reader["حالة الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderType, reader["نوع الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeName, reader["اسم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelDepartment, reader["القسم"].ToString());
                        SetLabelVisibilityAndText(LabelNotes, reader["تفاصيل مقدم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelJobTitle, reader["الوظيفة"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeNumber, reader["رقم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelCivilRegistry, reader["السجل المدني"].ToString());
                        SetLabelVisibilityAndText(LabelNationality, reader["الجنسية"].ToString());
                        SetLabelVisibilityAndText(LabelMobileNumber, reader["رقم الجوال"].ToString());
                        SetLabelVisibilityAndText(LabelEmploymentType, reader["نوع التوظيف"].ToString());
                        SetLabelVisibilityAndText(LabelQualification, reader["المؤهل"].ToString());
                        SetLabelVisibilityAndText(LabelManagerApproval, reader["تم التأكيد/الإلغاء من مدير القسم"].ToString());
                        SetLabelVisibilityAndText(LabelSupervisorApproval, reader["تم التأكيد/الإلغاء من قبل المشرف"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorApproval, reader["تم التحويل/الإلغاء من قبل المنسق"].ToString());
                        SetLabelVisibilityAndText(LabelCancellationReason, reader["سبب الإلغاء/الإعادة"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorDetails, reader["تفاصيل المنسق"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalServicesPermission, reader["مشرف خدمات الموظفين"].ToString());
                        SetLabelVisibilityAndText1(LabelHRPlanningPermission, reader["مشرف إدارة تخطيط الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelITPermission, reader["مشرف إدارة تقنية المعلومات"].ToString());
                        SetLabelVisibilityAndText1(LabelAttendanceControlPermission, reader["مشرف مراقبة الدوام"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalRecordsPermission, reader["مشرف السجلات الطبية"].ToString());
                        SetLabelVisibilityAndText1(LabelPayrollPermission, reader["مشرف إدارة الرواتب والاستحقاقات"].ToString());
                        SetLabelVisibilityAndText1(LabelLegalCompliancePermission, reader["مشرف إدارة القانونية والالتزام"].ToString());
                        SetLabelVisibilityAndText1(LabelHRServicesPermission, reader["مشرف خدمات الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelHousingPermission, reader["مشرف إدارة الإسكان"].ToString());
                        SetLabelVisibilityAndText1(LabelFilesSectionPermission, reader["مشرف قسم الملفات"].ToString());
                        SetLabelVisibilityAndText1(LabelOutpatientPermission, reader["مشرف العيادات الخارجية"].ToString());
                        SetLabelVisibilityAndText1(LabelSocialInsurancePermission, reader["مشرف التأمينات الاجتماعية"].ToString());
                        SetLabelVisibilityAndText1(LabelInventoryControlPermission, reader["مشرف وحدة مراقبة المخزون"].ToString());
                        SetLabelVisibilityAndText1(LabelSelfResourcesPermission, reader["مشرف إدارة تنمية الإيرادات"].ToString());
                        SetLabelVisibilityAndText1(LabelNursingPermission, reader["مشرف إدارة الأمن و السلامة"].ToString());
                        SetLabelVisibilityAndText1(LabelEmployeeServicesPermission, reader["مشرف الطب الاتصالي"].ToString());
                        SetLabelVisibilityAndText(LabelHRManagerApproval, reader["مدير الموارد البشرية"].ToString());
                        OrderDetailsPanel.Visible = true;
                        restoreDetailsPanel.Visible = false;
                    }
                    else
                    {
                        OrderDetailsPanel.Visible = false;
                        restoreDetailsPanel.Visible = false;
                    }
                }
            }
        }

        // تقوم هذه الدالة بتعيين نص معين إلى عنصر Label وتحديد ما إذا كان يجب إظهاره أو إخفاؤه:
        // 1. إذا كان النص الممرر غير فارغ أو يحتوي على مسافات فقط، يتم التحقق مما إذا كان يمكن تحويله إلى تاريخ.
        // 2. إذا كان النص يمثل تاريخًا، يتم تحويله إلى صيغة "yyyy-MM-dd" وعرضه في Label.
        // 3. إذا لم يكن النص يمثل تاريخًا، يتم تعيين النص كما هو إلى Label.
        // 4. إذا كان النص فارغًا أو null، يتم إخفاء الـ Label.
        private void SetLabelVisibilityAndText(Label label, string text)
        {
            if (!string.IsNullOrWhiteSpace(text))
            {


                DateTime dateValue;
                if (DateTime.TryParse(text, out dateValue))
                {
                    label.Text = dateValue.ToString("yyyy-MM-dd");
                    label.Visible = true;
                }
                else
                {

                    label.Text = text;
                    label.Visible = true;
                }
            }
            else
            {
                label.Visible = false;
            }


        }

        // تقوم هذه الدالة بتعيين نص إلى عنصر Label مع تعديل النص إذا احتوى على عبارة "اعتماد بواسطة":
        // 1. إذا كان النص يحتوي على "اعتماد بواسطة"، تتم إزالة هذه العبارة.
        // 2. إذا كان النص فارغًا أو null، يتم تعيين النص إلى "/".
        // 3. يتم عرض الـ Label وتعيين النص المعدل إليه.

        private void SetLabelVisibilityAndText1(Label label, string text)
        {
            if (text.Contains("اعتماد بواسطة"))
            {
                text = text.Replace("اعتماد بواسطة", "").Trim();
            }

            if (string.IsNullOrEmpty(text))
            {
                text = "/";
            }

            label.Visible = true; // Make sure the label is visible
            label.Text = text; // Set the text of the label

        }


        // تقوم هذه الدالة بتحميل أرقام الطلبات في القائمة المنسدلة بناءً على صلاحية المستخدم:
        // 1. التحقق من صلاحية المستخدم المخزنة في الجلسة.
        // 2. إذا كانت الصلاحية "منسق الموارد البشرية"، يتم استرجاع أرقام الطلبات التي حالتها '(B)' أو 'أُعيد بواسطة أحد المشرفين' من قاعدة البيانات.
        // 3. يتم تعيين البيانات المسترجعة إلى القائمة المنسدلة (ddlOrderNumbers).
        // 4. يتم إضافة خيار افتراضي في بداية القائمة بعنوان "-- اختر رقم الطلب --".
        private void PopulateOrderNumbers()
        {
            if (Session["UserPermission"]?.ToString().Trim() != "منسق الموارد البشرية")
            {
                ddlOrderNumbers.Items.Insert(0, new ListItem("-- اختر رقم الطلب --", ""));
                return;
            }

            try
            {
                using (var con = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = @"
                SELECT DISTINCT
                    [رقم الطلب],
                    [اسم الموظف],
                    CASE 
                        WHEN [اسم الموظف] IS NULL OR [اسم الموظف] = '' 
                        THEN CAST([رقم الطلب] AS NVARCHAR(50))
                        ELSE CAST([رقم الطلب] AS NVARCHAR(50)) + ' | ' + [اسم الموظف]
                    END AS DisplayText
                FROM ordersTable 
                WHERE [حالة الطلب] IN (
                    '(B)', 
                    N'أُعيد بواسطة أحد المشرفين',
                    N'أُعيد بواسطة المدير',
                    N'يتطلب إجراءات'
                )
                ORDER BY [رقم الطلب] DESC";

                    using (var cmd = new SqlCommand(query, con))
                    {
                        con.Open();
                        using (var reader = cmd.ExecuteReader())
                        {
                            ddlOrderNumbers.Items.Clear();
                            ddlOrderNumbers.DataSource = reader;
                            ddlOrderNumbers.DataTextField = "DisplayText";
                            ddlOrderNumbers.DataValueField = "رقم الطلب";
                            ddlOrderNumbers.DataBind();
                        }
                    }
                }

                ddlOrderNumbers.Items.Insert(0, new ListItem("-- اختر رقم الطلب --", ""));
            }
            catch (Exception ex)
            {
                // يمكن إضافة معالجة الأخطاء هنا
                System.Diagnostics.Debug.WriteLine($"خطأ في PopulateOrderNumbers: {ex.Message}");
            }
        }

        // تقوم هذه الدالة دالة معالجة الضغط على زر "يتطلب إجراءات
        protected void btnNeedsAction_Click(object sender, EventArgs e)
        {
            if (ddlOrderNumbers.SelectedIndex == 0)
            {
                LabelError.Text = "يرجى اختيار رقم الطلب.";
                LabelError.Visible = true;
                return;
            }

            string actionDetails = txtActionRequired.Text.Trim();
            if (string.IsNullOrEmpty(actionDetails))
            {
                LabelError.Text = "يرجى إدخال الإجراءات المطلوبة.";
                LabelError.Visible = true;
                return;
            }

            string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
            string confirmedBy = Session["Username"]?.ToString();
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"UPDATE ordersTable 
                        SET [حالة الطلب] = N'يتطلب إجراءات',
                            [تم التحويل/الإلغاء من قبل المنسق] = @ConfirmedBy,
                            [ملاحظات المشرفين] = @ActionDetails
                        WHERE [رقم الطلب] = @OrderNumber";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                    cmd.Parameters.AddWithValue("@ConfirmedBy", confirmedBy);
                    cmd.Parameters.AddWithValue("@ActionDetails", actionDetails);

                    con.Open();
                    int result = cmd.ExecuteNonQuery();

                    if (result > 0)
                    {
                        LabelMessage.Text = "تم حفظ طلب الإجراءات بنجاح.";
                        LabelMessage.Visible = true;
                        LabelError.Visible = false;
                        OrderDetailsPanel.Visible = false;
                        ClearFields();
                        PopulateOrderNumbers();
                    }
                    else
                    {
                        LabelError.Text = "حدث خطأ أثناء حفظ الإجراءات.";
                        LabelError.Visible = true;
                    }
                }
            }
        }


        // تقوم هذه الدالة بمعالجة تحويل الطلب للاعتماد عند الضغط على زر "Submit":
        // 1. تتحقق أولاً من أن المستخدم قد اختار رقم الطلب المناسب من القائمة.
        // 2. تتحقق من أن حقل "التفاصيل/الرقم" تم ملؤه، وإذا لم يتم، تظهر رسالة خطأ.
        // 3. تتحقق مما إذا تم اختيار أي قسم من قائمة "CheckBoxes". إذا لم يتم اختيار أي منها، تعرض رسالة خطأ.
        // 4. تقوم بتحديث الطلب في قاعدة البيانات، بما في ذلك الحقول مثل [تفاصيل المنسق] و[حالة الطلب].
        // 5. يتم تحديث القيم الخاصة بالأقسام المختارة، مع تعيين حالة الطلب كـ "الطلب تحت التنفيذ" مع التاريخ.
        // 6. تعرض رسالة نجاح إذا تم تحديث الطلب بنجاح، وتعيد تحميل قائمة أرقام الطلبات.
        protected void btnSubmit_Click(object sender, EventArgs e)
        {
            // Check if a valid order number is selected
            if (ddlOrderNumbers.SelectedIndex == 0)
            {
                LabelError.Text = "يرجى اختيار رقم الطلب.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
                return;
            }

            string rejectReason = txtRejectReason.Text.Trim();
            string rakam = TextBoxRakam.Text.Trim();

            if (rakam == "")
            {
                LabelError.Text = "يرجى كتابة التفاصيل/الرقم.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
                return;
            }

            string selectedOrderNumber = ddlOrderNumbers.SelectedValue; // Use SelectedValue to get the actual value
            string confirmedBy = Session["Username"]?.ToString(); // Retrieve the username from session
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                bool allUnchecked = true;

                foreach (Control control in checkboxContainer.Controls)
                {
                    if (control is CheckBox checkBox && checkBox.Checked)
                    {
                        allUnchecked = false;
                        break;
                    }
                }

                string query;

                if (allUnchecked)
                {
                    LabelError.Text = "يجب اختيار قسم على الأقل للاعتماد.";
                    LabelError.Visible = true;
                    LabelMessage.Visible = false;
                    return;
                }
                else
                {
                    // Concatenate the existing value of [تفاصيل المنسق] with the new value
                    query = "UPDATE ordersTable SET [حالة الطلب] = '(C)', " +
                            "[تم التحويل/الإلغاء من قبل المنسق] = @ConfirmedBy, " +
                            "[تفاصيل المنسق] = ISNULL([تفاصيل المنسق], '') + @Rakam, " +
                            "[سبب الإلغاء/الإعادة] = @RejectReason ,[نوع التحويل] = 'يدوي' WHERE [رقم الطلب] = @OrderNumber";
                }

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                    cmd.Parameters.AddWithValue("@ConfirmedBy", confirmedBy);

                    // إدخال التفاصيل فقط بدون التاريخ
                    cmd.Parameters.AddWithValue("@Rakam", $"{rakam}");
                    cmd.Parameters.AddWithValue("@RejectReason", string.Empty); // Set سبب الإلغاء to an empty value

                    con.Open();
                    int rowsAffected = cmd.ExecuteNonQuery();

                    if (rowsAffected > 0)
                    {
                        // Update specific columns based on the checkboxes
                        foreach (Control control in checkboxContainer.Controls)
                        {
                            if (control is CheckBox checkBox && checkBox.Checked)
                            {
                                // Extract column name from the checkbox text
                                string columnName = "مشرف " + checkBox.Text;

                                // Update the value only if the current value is either empty, '/' or starts with "تم الإعادة"
                                string updateQuery = $@"
                                 UPDATE ordersTable 
                                 SET [{columnName}] = @StatusWithDate 
                                 WHERE [رقم الطلب] = @OrderNumber 
                                 AND (ISNULL([{columnName}], '') = '' 
                                 OR [{columnName}] = '/' 
                                 OR [{columnName}] LIKE 'تم الإعادة%')";

                                using (SqlCommand updateCmd = new SqlCommand(updateQuery, con))
                                {
                                    // Set the status with the current date followed by "الطلب تحت التنفيذ"
                                    string statusWithDate = $"{DateTime.Now.ToString("yyyy-MM-dd")} الطلب تحت التنفيذ";
                                    updateCmd.Parameters.AddWithValue("@StatusWithDate", statusWithDate);
                                    updateCmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);

                                    updateCmd.ExecuteNonQuery();
                                }
                            }
                        }

                        LabelMessage.Text = "تم تحويل الطلب للاعتماد بنجاح.";
                        OrderDetailsPanel.Visible = false;
                        LabelMessage.Visible = true;
                        LabelError.Visible = false;

                        PopulateOrderNumbers(); // Refresh the list after updating
                        UpdateOrderStatuses();
                        SiteMaster master = (SiteMaster)Master;
                        if (master != null)
                        {
                            master.UpdateNewOrdersCount();
                        }

                        // تفريغ الحقول بعد النجاح
                        ClearFields();
                    }
                    else
                    {
                        LabelError.Text = "حدث خطأ أثناء التحويل للاعتماد.";
                        LabelError.Visible = true;
                        LabelMessage.Visible = false;
                    }
                }
            }
        }


        protected void UpdateOrderStatuses()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();

                // استرجاع جميع الصفوف من جدول الطلبات التي حالتها ليست "مقبول"
                string selectQuery = "SELECT * FROM ordersTable WHERE[حالة الطلب] <> 'مقبول'";
                DataTable ordersTable = new DataTable();

                using (SqlDataAdapter adapter = new SqlDataAdapter(selectQuery, con))
                {
                    adapter.Fill(ordersTable);// ملء الجدول بالبيانات المسترجعة
                }

                // تحضير أمر التحديث
                string updateQuery = "UPDATE ordersTable SET [حالة الطلب] = @RequestStatus WHERE [رقم الطلب] = @OrderNumber";
                using (SqlCommand updateCmd = new SqlCommand(updateQuery, con))
                {
                    updateCmd.Parameters.Add("@RequestStatus", SqlDbType.NVarChar);
                    updateCmd.Parameters.Add("@OrderNumber", SqlDbType.Int);

                    // التكرار عبر كل صف
                    foreach (DataRow row in ordersTable.Rows)
                    {
                        bool allEthenColumnsEmpty = true;// للتحقق إذا كانت جميع أعمدة المشرفين فارغة
                        bool anyColumnRejected = false; // للتحقق إذا تم إعادة الطلب من أحد المشرفين
                        bool allNonEmptyColumnsSigned = true;// للتحقق إذا تم التوقيع على جميع الأعمدة غير الفارغة

                        // التكرار عبر كل عمود من الأعمدة
                        foreach (DataColumn column in ordersTable.Columns)
                        {
                            if (column.ColumnName.StartsWith("مشرف"))// التحقق إذا كان العمود يخص مشرف
                            {
                                string columnValue = row[column]?.ToString();

                                if (!string.IsNullOrEmpty(columnValue))
                                {
                                    allEthenColumnsEmpty = false;// إذا كان العمود غير فارغ

                                    if (columnValue.Contains("تم الإعادة"))
                                    {
                                        anyColumnRejected = true; // إذا تم إعادة الطلب من المشرف
                                        break;// الخروج من الحلقة
                                    }
                                    else if (!columnValue.Contains("اعتماد بواسطة"))
                                    {
                                        allNonEmptyColumnsSigned = false;// إذا لم يتم التوقيع على العمود
                                    }
                                }
                            }
                        }
                        // إذا كان الطلب يحتوي على عمود مرفوض
                        if (!allEthenColumnsEmpty && anyColumnRejected)
                        {
                            updateCmd.Parameters["@RequestStatus"].Value = "أُعيد بواسطة أحد المشرفين";
                            updateCmd.Parameters["@OrderNumber"].Value = row["رقم الطلب"];
                            updateCmd.ExecuteNonQuery();
                        }
                        // إذا كانت جميع الأعمدة المملوءة تم التوقيع عليها
                        else if (!allEthenColumnsEmpty && allNonEmptyColumnsSigned)
                        {
                            updateCmd.Parameters["@RequestStatus"].Value = "(D)";
                            updateCmd.Parameters["@OrderNumber"].Value = row["رقم الطلب"];
                            updateCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
        }



        private string CleanFileName(string fileName)
        {
            // إزالة الأحرف غير المسموح بها في أسماء الملفات
            char[] invalidChars = System.IO.Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            // التأكد من أن الاسم لا يتجاوز طولاً معيناً
            const int maxLength = 100;
            if (fileName.Length > maxLength)
            {
                string extension = System.IO.Path.GetExtension(fileName);
                fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
            }

            return fileName;
        }

        protected void btnDownload_Click(object sender, EventArgs e)
        {
            try
            {
                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"SELECT ملف1, ملف2, ملف3, ملف4, [اسم الموظف] 
                           FROM ordersTable 
                           WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        con.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string employeeName = reader["اسم الموظف"].ToString().Trim();
                                bool filesFound = false;

                                using (MemoryStream zipStream = new MemoryStream())
                                {
                                    using (ZipArchive zipArchive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
                                    {
                                        for (int i = 0; i < 4; i++)
                                        {
                                            byte[] compressedData = reader[i] as byte[];
                                            if (compressedData != null && compressedData.Length > 0)
                                            {
                                                try
                                                {
                                                    // فك ضغط البيانات
                                                    byte[] pdfData = FileCompressor.ExtractFile(compressedData);

                                                    if (pdfData != null && IsPdfFile(pdfData))
                                                    {
                                                        filesFound = true;
                                                        string fileName = CleanFileName($"مرفق_{i + 1}_طلب_{selectedOrderNumber}_{employeeName}.pdf");

                                                        ZipArchiveEntry zipEntry = zipArchive.CreateEntry(fileName);
                                                        using (Stream entryStream = zipEntry.Open())
                                                        {
                                                            entryStream.Write(pdfData, 0, pdfData.Length);
                                                        }

                                                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة الملف: {fileName}");
                                                    }
                                                    else
                                                    {
                                                        System.Diagnostics.Debug.WriteLine($"❌ الملف {i + 1} ليس PDF صالح");
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الملف {i + 1}: {ex.Message}");
                                                    continue;  // استمر مع الملف التالي
                                                }
                                            }
                                        }
                                    }

                                    if (filesFound)
                                    {
                                        string zipFileName = CleanFileName($"مرفقات_طلب_{selectedOrderNumber}_{employeeName}.zip");

                                        Response.Clear();
                                        Response.ContentType = "application/zip";
                                        Response.AddHeader("Content-Disposition", $"attachment; filename={zipFileName}");
                                        Response.BinaryWrite(zipStream.ToArray());
                                        Response.Flush();
                                        Response.End();
                                    }
                                    else
                                    {
                                        LabelError.Text = "لا توجد ملفات صالحة لتحميلها.";
                                        LabelError.Visible = true;
                                    }
                                }
                            }
                            else
                            {
                                LabelError.Text = "لم يتم العثور على الطلب.";
                                LabelError.Visible = true;
                            }
                        }
                    }
                }
            }
            catch (System.Threading.ThreadAbortException)
            {
                // تجاهل
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل الملفات: " + ex.Message;
                LabelError.Visible = true;
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام: {ex.Message}");
            }
        }

        private bool IsPdfFile(byte[] data)
        {
            if (data == null || data.Length < 4)
                return false;

            // التحقق من PDF header
            return data[0] == 0x25 && // %
                   data[1] == 0x50 && // P
                   data[2] == 0x44 && // D
                   data[3] == 0x46;   // F
        }



        

        /// <summary>
        /// التحقق من أن الملف هو PDF صالح
        /// </summary>
        private bool ValidatePdfFormat(byte[] data)
        {
            try
            {
                if (data == null || data.Length < 4)
                {
                    System.Diagnostics.Debug.WriteLine("❌ البيانات فارغة أو قصيرة جداً");
                    return false;
                }

                // فحص توقيع PDF (PDF Signature)
                bool isPdf = data[0] == 0x25 && // %
                            data[1] == 0x50 && // P
                            data[2] == 0x44 && // D
                            data[3] == 0x46;   // F

                if (!isPdf)
                {
                    System.Diagnostics.Debug.WriteLine("❌ الملف ليس بصيغة PDF صالحة");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم التحقق من صحة ملف PDF");
                }

                return isPdf;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص نوع الملف: {ex.Message}");
                return false;
            }
        }



        





        // تقوم هذه الدالة بإعادة تعيين جميع الحقول إلى حالتها الافتراضية:
        // 1. تعيين قائمة أرقام الطلبات إلى الخيار الافتراضي (أول عنصر).
        // 2. تفريغ حقل "التفاصيل/الرقم".
        // 3. تفريغ حقل "سبب الإلغاء".
        // 4. إلغاء تحديد جميع العناصر في مجموعة الـ CheckBoxes.
        private void ClearFields()
        {
            ddlOrderNumbers.SelectedIndex = 0; // إعادة تعيين قائمة الطلبات
            TextBoxRakam.Text = string.Empty; // تفريغ حقل الرقم
            txtRejectReason.Text = string.Empty; // تفريغ حقل سبب الإلغاء

            // إلغاء تحديد جميع الـ CheckBoxes
            foreach (Control control in checkboxContainer.Controls)
            {
                if (control is CheckBox checkBox)
                {
                    checkBox.Checked = false;
                }
            }
        }




        private readonly string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

        protected void btnReturnOrder_Click(object sender, EventArgs e)
        {
            try
            {
                if (ddlOrderNumbers.SelectedIndex == 0)
                {
                    ShowError("يرجى اختيار رقم الطلب.");
                    return;
                }

                string rejectReason = txtRejectReason.Text.Trim();
                if (string.IsNullOrEmpty(rejectReason))
                {
                    ShowError("يرجى إدخال سبب الإعادة.");
                    return;
                }

                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string confirmedBy = Session["Username"]?.ToString();
                string currentDate = DateTime.Now.ToString("yyyy-MM-dd");
                string returnStatus = $"{currentDate} | تم الإعادة بواسطة {confirmedBy}";

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();
                    using (var transaction = con.BeginTransaction())
                    {
                        try
                        {
                            // الحصول على القسم ونوع مساعد المدير
                            string deptQuery = @"
                        SELECT d.AssistantManagerID 
                        FROM ordersTable o
                        JOIN Departments d ON o.[القسم] = d.[الأقسام]
                        WHERE o.[رقم الطلب] = @OrderNumber";

                            string assistantManagerId;
                            using (var cmdDept = new SqlCommand(deptQuery, con, transaction))
                            {
                                cmdDept.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                assistantManagerId = cmdDept.ExecuteScalar()?.ToString() ?? "غير معروف";
                            }
                            string newStatus;
                            if (assistantManagerId == "B")
                            {
                                // إذا كان القسم من نوع B، أعد الطلب إلى مدير القسم
                                newStatus = "(DM)";
                            }
                            else
                            {
                                // للأقسام الأخرى، أعد الطلب إلى مساعد المدير المناسب
                                newStatus = $"({assistantManagerId})";
                            }

                            // تحديث الطلب
                            string updateQuery = @"
                    UPDATE ordersTable 
                    SET [حالة الطلب] = '(DM)',
                        [تم التحويل/الإلغاء من قبل المنسق] = @ReturnStatus,
                        [سبب الإلغاء/الإعادة] = @RejectReason
                    WHERE [رقم الطلب] = @OrderNumber";

                            using (var cmdUpdate = new SqlCommand(updateQuery, con, transaction))
                            {
                                cmdUpdate.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                cmdUpdate.Parameters.AddWithValue("@NewStatus", newStatus);  // استخدام newStatus بدلاً من assistantManagerId
                                cmdUpdate.Parameters.AddWithValue("@ReturnStatus", returnStatus);
                                cmdUpdate.Parameters.AddWithValue("@RejectReason", rejectReason);
                                cmdUpdate.ExecuteNonQuery();
                            }

                            transaction.Commit();
                            ShowSuccess("تم إعادة الطلب بنجاح.");
                            UpdateUI();
                            ClearFields();
                        }
                        catch (Exception )
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex);
                ShowError("حدث خطأ أثناء إعادة الطلب: " + ex.Message);
            }
        }
        private void UpdateUI()
        {
            OrderDetailsPanel.Visible = false;
            PopulateOrderNumbers();
            if (Master is SiteMaster master)
            {
                master.UpdateNewOrdersCount();
            }
        }

        private void ShowError(string message)
        {
            LabelError.Text = message;
            LabelError.Visible = true;
            LabelMessage.Visible = false;
            restoreDetailsPanel.Visible = false;
        }




        private void ShowSuccess(string message)
        {
            LabelMessage.Text = message;
            LabelMessage.Visible = true;
            LabelError.Visible = false;
        }
        private void ClearForm()
        {
            txtRestoreNotes.Text = "";
            restoreDetailsPanel.Visible = false;
            ddlRestoreOrders.SelectedIndex = 0;
        }

        // تقوم هذه الدالة بمعالجة عملية إلغاء الطلب من قبل المنسق.
        // تتحقق أولاً من اختيار رقم الطلب وإدخال سبب الإلغاء.
        // في حالة إدخال البيانات بشكل صحيح، يتم تحديث حالة الطلب إلى "تم الإلغاء من قبل المنسق" 
        // وتسجيل سبب الإلغاء والموظف الذي قام بالإلغاء.
        // بعد نجاح العملية، يتم تحديث واجهة المستخدم بإخفاء تفاصيل الطلب، 
        // عرض رسالة نجاح، وتفريغ الحقول وإعادة تحميل قائمة الطلبات.
        protected void btnReject_Click(object sender, EventArgs e)
        {
            // Check if a valid order number is selected
            if (ddlOrderNumbers.SelectedIndex == 0)
            {
                LabelError.Text = "يرجى اختيار رقم الطلب.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
                return;
            }

            string rejectReason = txtRejectReason.Text.Trim();

            if (string.IsNullOrEmpty(rejectReason))
            {
                LabelError.Text = "يرجى إدخال سبب الإلغاء.";
                LabelError.Visible = true;
                return;
            }


            string selectedOrderNumber = ddlOrderNumbers.SelectedValue; // Use SelectedValue to get the actual value
            string confirmedBy = Session["Username"]?.ToString(); // Retrieve the username from session
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = "UPDATE ordersTable SET [حالة الطلب] = 'تم الإلغاء من قبل المنسق', [تم التحويل/الإلغاء من قبل المنسق] = @ConfirmedBy, [سبب الإلغاء/الإعادة] = @RejectReason WHERE [رقم الطلب] = @OrderNumber";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                    cmd.Parameters.AddWithValue("@ConfirmedBy", confirmedBy);
                    cmd.Parameters.AddWithValue("@RejectReason", rejectReason);
                    con.Open();
                    int rowsAffected = cmd.ExecuteNonQuery();

                    if (rowsAffected > 0)
                    {


                        LabelMessage.Text = "تم إلغاء الطلب بنجاح.";

                        OrderDetailsPanel.Visible = false;
                        LabelMessage.Visible = true;
                        LabelError.Visible = false;
                        PopulateOrderNumbers(); // Refresh the list after updating
                        SiteMaster master = (SiteMaster)Master;
                        if (master != null)
                        {
                            master.UpdateNewOrdersCount();
                        }
                        // تفريغ الحقول بعد النجاح
                        ClearFields();

                    }
                    else
                    {
                        LabelError.Text = "حدث خطأ .";
                        LabelError.Visible = true;
                        LabelMessage.Visible = false;
                    }
                }
            }
        }

        // تقوم هذه الدالة باسترجاع قائمة من المسارات من قاعدة البيانات بناءً على اسم العمود المحدد.
        // تستخدم سلسلة الاتصال المستخرجة من ملف web.config لإجراء الاتصال بقاعدة البيانات.
        // يتم تنفيذ استعلام SQL لاسترجاع القيم النصية من العمود المطلوب.
        // في النهاية، تعيد الدالة قائمة من المسارات المسترجعة من قاعدة البيانات.


        private List<string> GetPathsFromDatabase(string columnName)
        {
            List<string> paths = new List<string>();

            // استرجاع سلسلة الاتصال من ملف web.config
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            // التحقق من اسم العمود لتجنب SQL Injection
            var validColumns = new List<string> { "مسار1", "مسار2", "مسار3", "مسار4", "مسار5", "مسار6" };
            if (!validColumns.Contains(columnName))
            {
                throw new ArgumentException("Invalid column name");
            }

            // استعلام SQL لاسترجاع المسارات من العمود المحدد
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                string query = $"SELECT {columnName} FROM PathsTable WHERE {columnName} IS NOT NULL"; // استثناء القيم الفارغة
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            // التحقق من وجود قيمة صالحة قبل الإضافة
                            string path = reader[columnName].ToString();
                            if (!string.IsNullOrEmpty(path))
                            {
                                paths.Add(path);
                            }
                        }
                    }
                }
            }

            return paths;
        }



        // تقوم هذه الدالة بتحميل البيانات من قاعدة البيانات بناءً على مسار محدد "مسار1".
        // يتم جلب المسارات المخزنة في قاعدة البيانات ومقارنتها بنص كل CheckBox داخل الـ checkboxContainer.
        // إذا تطابق نص CheckBox مع مسار من قاعدة البيانات، يتم تحديد الـ CheckBox.
        // أما إذا لم يكن هناك تطابق، فيتم إلغاء التحديد.
        protected void Path1_Click(object sender, EventArgs e)
        {
            // استرجاع البيانات من جدول PathsTable بناءً على العمود "مسار1"
            List<string> pathsFromDatabase = GetPathsFromDatabase("مسار1"); // Implement this method

            // التكرار عبر كل عنصر تحكم داخل checkboxContainer
            foreach (Control control in checkboxContainer.Controls)
            {
                if (control is CheckBox checkBox)
                {
                    // التحقق مما إذا كان نص الـ CheckBox موجودًا ضمن المسارات المسترجعة من قاعدة البيانات
                    if (pathsFromDatabase.Contains(checkBox.Text))
                    {
                        checkBox.Checked = true; // تحديد الـ CheckBox إذا كان النص الخاص به يتطابق مع مسار من قاعدة البيانات
                    }
                    else
                    {
                        checkBox.Checked = false; // إلغاء تحديد الـ CheckBox إذا كان النص الخاص به لا يتطابق مع أي مسار من قاعدة البيانات
                    }
                }
            }
        }


        protected void Path2_Click(object sender, EventArgs e)
        {

            // استرجاع البيانات من جدول PathsTable بناءً على عمود "مسار2"
            List<string> pathsFromDatabase = GetPathsFromDatabase("مسار2"); // قم بتنفيذ هذه الطريقة

            // التكرار عبر كل عنصر تحكم في الحاوية checkboxContainer
            foreach (Control control in checkboxContainer.Controls)
            {
                if (control is CheckBox checkBox)
                {
                    // // التحقق مما إذا كان نص مربع الاختيار موجودًا في المسارات المسترجعة من قاعدة البيانات
                    if (pathsFromDatabase.Contains(checkBox.Text))
                    {
                        checkBox.Checked = true; // تحديد مربع الاختيار إذا كان نصه يتطابق مع أحد المسارات من قاعدة البيانات
                    }
                    else
                    {
                        checkBox.Checked = false; // إلغاء تحديد مربع الاختيار إذا لم يتطابق النص مع أي مسار من قاعدة البيانات
                    }
                }
            }
        }

      

        protected void Path3_Click(object sender, EventArgs e)
        {

            // استرجاع البيانات من جدول PathsTable بناءً على عمود "مسار3"
            List<string> pathsFromDatabase = GetPathsFromDatabase("مسار3"); // قم بتنفيذ هذه الطريقة

            // التكرار عبر كل عنصر تحكم في الحاوية checkboxContainer
            foreach (Control control in checkboxContainer.Controls)
            {
                if (control is CheckBox checkBox)
                {
                    // التحقق مما إذا كان نص مربع الاختيار موجودًا في المسارات المسترجعة من قاعدة البيانات
                    if (pathsFromDatabase.Contains(checkBox.Text))
                    {
                        checkBox.Checked = true; // تحديد مربع الاختيار إذا كان نصه يتطابق مع أحد المسارات من قاعدة البيانات
                    }
                    else
                    {
                        checkBox.Checked = false; // إلغاء تحديد مربع الاختيار إذا لم يتطابق النص مع أي مسار من قاعدة البيانات
                    }
                }
            }
        }
        protected void Path4_Click(object sender, EventArgs e)
        {
            // استرجاع البيانات من جدول PathsTable بناءً على عمود "مسار4"
            List<string> pathsFromDatabase = GetPathsFromDatabase("مسار4"); // قم بتنفيذ هذه الطريقة

            // التكرار عبر كل عنصر تحكم في الحاوية checkboxContainer
            foreach (Control control in checkboxContainer.Controls)
            {
                if (control is CheckBox checkBox)
                {
                    // التحقق مما إذا كان نص مربع الاختيار موجودًا في المسارات المسترجعة من قاعدة البيانات
                    if (pathsFromDatabase.Contains(checkBox.Text))
                    {
                        checkBox.Checked = true; // تحديد مربع الاختيار إذا كان النص يتطابق مع أحد المسارات من قاعدة البيانات
                    }
                    else
                    {
                        checkBox.Checked = false; // إلغاء تحديد مربع الاختيار إذا لم يتطابق النص مع أي مسار من قاعدة البيانات
                    }
                }
            }
        }
        protected void Path5_Click(object sender, EventArgs e)
        {
            // استرجاع البيانات من جدول PathsTable بناءً على عمود "مسار5"
            List<string> pathsFromDatabase = GetPathsFromDatabase("مسار5"); // قم بتنفيذ هذه الطريقة

            // التكرار عبر كل عنصر تحكم في الحاوية checkboxContainer
            foreach (Control control in checkboxContainer.Controls)
            {
                if (control is CheckBox checkBox)
                {
                    // التحقق مما إذا كان نص مربع الاختيار موجودًا في المسارات المسترجعة من قاعدة البيانات
                    if (pathsFromDatabase.Contains(checkBox.Text))
                    {
                        checkBox.Checked = true; // تحديد مربع الاختيار إذا كان النص يتطابق مع أحد المسارات من قاعدة البيانات
                    }
                    else
                    {
                        checkBox.Checked = false; // إلغاء تحديد مربع الاختيار إذا لم يتطابق النص مع أي مسار من قاعدة البيانات
                    }
                }
            }
        }
        protected void Path6_Click(object sender, EventArgs e)
        {
            // استرجاع البيانات من جدول PathsTable بناءً على عمود "مسار6"
            List<string> pathsFromDatabase = GetPathsFromDatabase("مسار6"); // قم بتنفيذ هذه الطريقة

            // التكرار عبر كل عنصر تحكم في الحاوية checkboxContainer
            foreach (Control control in checkboxContainer.Controls)
            {
                if (control is CheckBox checkBox)
                {
                    // التحقق مما إذا كان نص مربع الاختيار موجودًا في المسارات المسترجعة من قاعدة البيانات
                    if (pathsFromDatabase.Contains(checkBox.Text))
                    {
                        checkBox.Checked = true; // تحديد مربع الاختيار إذا كان النص يتطابق مع أحد المسارات من قاعدة البيانات
                    }
                    else
                    {
                        checkBox.Checked = false; // إلغاء تحديد مربع الاختيار إذا لم يتطابق النص مع أي مسار من قاعدة البيانات
                    }
                }
            }
        }


        // تقوم هذه الدالة بتحديد جميع مربعات الاختيار الموجودة في الحاوية checkboxContainer.
        protected void ButtonSelectAll_Click(object sender, EventArgs e)
        {
            // التكرار عبر كل عنصر تحكم في الحاوية checkboxContainer
            foreach (Control control in checkboxContainer.Controls)
            {
                if (control is CheckBox checkBox)
                {

                    checkBox.Checked = true; // تحديد جميع مربعات الاختيار

                }
            }

        }






        // تقوم هذه الدالة بإلغاء تحديد جميع مربعات الاختيار الموجودة في الحاوية checkboxContainer.

        protected void ButtonUnSelectAll_Click(object sender, EventArgs e)
        {
            // التكرار عبر كل عنصر تحكم في الحاوية checkboxContainer
            foreach (Control control in checkboxContainer.Controls)
            {
                if (control is CheckBox checkBox)
                {

                    checkBox.Checked = false; // إلغاء تحديد جميع مربعات الاختيار

                }
            }
        }



        protected void btnAutoPath_Click(object sender, EventArgs e)
        {
            try
            {
                // 1. التحقق من اختيار طلب
                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                if (ddlOrderNumbers.SelectedIndex == 0)
                {
                    ShowAlert("يرجى اختيار رقم الطلب", "warning");
                    return;
                }

                // التحقق من إدخال التفاصيل والرقم
                string rakam = TextBoxRakam.Text.Trim();
                if (string.IsNullOrEmpty(rakam))
                {
                    // إظهار تنبيه إلزامية كتابة التفاصيل
                    ShowAlert("يرجى كتابة التفاصيل/الرقم", "warning");
                    LabelError.Text = "يرجى كتابة التفاصيل/الرقم.";
                    LabelError.ForeColor = System.Drawing.Color.Red;
                    LabelError.Visible = true;

                    // التركيز على حقل التفاصيل وتغيير لون الحقل للفت الانتباه
                    TextBoxRakam.Focus();
                    TextBoxRakam.CssClass = "form-control border-warning";
                    return;
                }

                // إعادة لون الحقل للوضع الطبيعي
                TextBoxRakam.CssClass = "form-control";

                // 2. الحصول على نوع الطلب والجنسية
                string orderType = LabelOrderType.Text;
                string nationality = LabelNationality.Text;
                string job = LabelJobTitle.Text;

                // 3. التحقق من وجود مسار تلقائي
                bool success = ApplyAutomaticRouting(selectedOrderNumber, orderType, nationality,job);

                if (success)
                {
                    // تحديث واجهة المستخدم
                    btnAutoPath.CssClass = "auto-path-btn auto-path-active";
                    btnPath1.Enabled = false;
                    btnPath2.Enabled = false;
                    btnPath3.Enabled = false;
                    btnPath4.Enabled = false;
                    btnPath5.Enabled = false;
                    btnPath6.Enabled = false;
                    // تحديث القائمة وإخفاء التفاصيل
                    PopulateOrderNumbers();
                    OrderDetailsPanel.Visible = false;

                    // تفريغ حقل التفاصيل
                    TextBoxRakam.Text = string.Empty;

                    // عرض رسائل النجاح
                    ShowAlert("تم تحويل الطلب للاعتماد بنجاح", "success");
                    LabelMessage.Text = "تم تحويل الطلب للاعتماد بنجاح.";
                    LabelMessage.Visible = true;
                    LabelError.Visible = false;

                    // تحديث عداد الطلبات الجديدة
                    if (Master is SiteMaster master)
                    {
                        master.UpdateNewOrdersCount();
                    }

                    // تحديث حالة الطلبات
                    UpdateOrderStatuses();
                }
                else
                {
                    // عرض رسائل الخطأ
                    ShowAlert("لم يتم العثور على مسار مناسب لهذا النوع من الطلبات", "warning");
                    string errorMessage = "لم يتم العثور على مسار مناسب لهذا النوع من الطلبات";
                    LabelError.Text = errorMessage;
                    LabelError.Visible = true;
                    LabelMessage.Visible = false;
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء التوجيه التلقائي: " + ex.Message, "error");
                LogError(ex);
            }
        }


        private bool ApplyAutomaticRouting(string orderNumber, string orderType, string nationality, string job)
        {
            // إضافة تتبع للقيم المستلمة للتأكد من صحتها
            System.Diagnostics.Debug.WriteLine("=== بدء التوجيه التلقائي ===");
            System.Diagnostics.Debug.WriteLine($"رقم الطلب: {orderNumber}");
            System.Diagnostics.Debug.WriteLine($"نوع الطلب: {orderType}");
            System.Diagnostics.Debug.WriteLine($"الجنسية قبل المعالجة: {nationality}");
            System.Diagnostics.Debug.WriteLine($"الوظيفة: {job}");

            // معالجة الجنسية باستخدام نفس منطق المسار السريع
            nationality = ProcessNationality(nationality);
            System.Diagnostics.Debug.WriteLine($"الجنسية بعد المعالجة: {nationality}");

            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();
                using (SqlTransaction transaction = conn.BeginTransaction())
                {
                    try
                    {
                        // 1. البحث عن المسار التلقائي
                        string routingQuery = @"
                    SELECT المشرفين as Supervisors
                    FROM AutoRouting 
                    WHERE نوع_الطلب = @OrderType 
                    AND الجنسية = @Nationality
                    AND حالة_المسار = 1
                    AND (الوظيفة = 'ALL' 
                        OR الوظيفة = @Job
                        OR (الوظيفة = 'ALL_EXCEPT_OTHER' AND @Job NOT LIKE N'أخرى - Other:%'))";

                        // تتبع الاستعلام وقيمه
                        System.Diagnostics.Debug.WriteLine("=== استعلام البحث عن المسار ===");
                        System.Diagnostics.Debug.WriteLine($"الاستعلام: {routingQuery}");

                        string supervisors;
                        using (SqlCommand cmd = new SqlCommand(routingQuery, conn, transaction))
                        {
                            // إضافة المعاملات مع التأكد من تنظيف البيانات
                            cmd.Parameters.AddWithValue("@OrderType", orderType?.Trim());
                            cmd.Parameters.AddWithValue("@Nationality", nationality?.Trim());
                            cmd.Parameters.AddWithValue("@Job", job?.Trim());

                            System.Diagnostics.Debug.WriteLine($"المعاملات: OrderType='{orderType}', Nationality='{nationality}', Job='{job}'");

                            var result = cmd.ExecuteScalar();
                            System.Diagnostics.Debug.WriteLine($"نتيجة الاستعلام: {(result == null ? "لا يوجد" : result.ToString())}");

                            if (result == null || result == DBNull.Value)
                            {
                                System.Diagnostics.Debug.WriteLine("لم يتم العثور على مسار تلقائي");
                                transaction.Rollback();
                                return false;
                            }
                            supervisors = result.ToString();
                            System.Diagnostics.Debug.WriteLine($"المشرفون: {supervisors}");
                        }

                        // 2. التحقق من وجود تفاصيل المنسق
                        string rakam = TextBoxRakam.Text?.Trim();
                        if (string.IsNullOrEmpty(rakam))
                        {
                            System.Diagnostics.Debug.WriteLine("لم يتم إدخال التفاصيل/الرقم");
                            ShowAlert("يرجى إدخال التفاصيل/الرقم", "warning");
                            transaction.Rollback();
                            return false;
                        }

                        // 3. تحديث حالة الطلب
                        string updateQuery = @"
                    UPDATE ordersTable 
                    SET [حالة الطلب] = '(C)',
                        [تم التحويل/الإلغاء من قبل المنسق] = @ConfirmedBy,
                        [تفاصيل المنسق] = @Rakam,
                        [نوع التحويل] = 'تلقائي'
                    WHERE [رقم الطلب] = @OrderNumber";

                        System.Diagnostics.Debug.WriteLine("=== تحديث حالة الطلب ===");
                        using (SqlCommand cmd = new SqlCommand(updateQuery, conn, transaction))
                        {
                            cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                            cmd.Parameters.AddWithValue("@ConfirmedBy", Session["Username"]?.ToString());
                            cmd.Parameters.AddWithValue("@Rakam", rakam);

                            int rowsAffected = cmd.ExecuteNonQuery();
                            System.Diagnostics.Debug.WriteLine($"عدد الصفوف المتأثرة: {rowsAffected}");

                            if (rowsAffected == 0)
                            {
                                System.Diagnostics.Debug.WriteLine("فشل تحديث حالة الطلب");
                                transaction.Rollback();
                                return false;
                            }
                        }

                        // 4. تحديث المشرفين
                        System.Diagnostics.Debug.WriteLine("=== تحديث المشرفين ===");
                        foreach (string supervisor in supervisors.Split(';'))
                        {
                            string trimmedSupervisor = supervisor.Trim();
                            string columnName = "مشرف " + trimmedSupervisor;

                            System.Diagnostics.Debug.WriteLine($"تحديث المشرف: {trimmedSupervisor}");

                            string supervisorQuery = $@"
                        UPDATE ordersTable 
                        SET [{columnName}] = @Status
                        WHERE [رقم الطلب] = @OrderNumber
                        AND (ISNULL([{columnName}], '') = '' 
                        OR [{columnName}] = '/' 
                        OR [{columnName}] LIKE 'تم الإعادة%')";

                            using (SqlCommand cmd = new SqlCommand(supervisorQuery, conn, transaction))
                            {
                                string status = $"{DateTime.Now:yyyy-MM-dd} الطلب تحت التنفيذ";
                                cmd.Parameters.AddWithValue("@Status", status);
                                cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);

                                int rowsAffected = cmd.ExecuteNonQuery();
                                System.Diagnostics.Debug.WriteLine($"عدد الصفوف المتأثرة للمشرف {trimmedSupervisor}: {rowsAffected}");
                            }
                        }

                        transaction.Commit();
                        System.Diagnostics.Debug.WriteLine("=== تم إكمال العملية بنجاح ===");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"=== حدث خطأ ===");
                        System.Diagnostics.Debug.WriteLine($"نوع الخطأ: {ex.GetType().Name}");
                        System.Diagnostics.Debug.WriteLine($"رسالة الخطأ: {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");

                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }










        // دالة إعادة تعيين حالة الأزرار
        protected void ResetButtonsState()
        {
            // التحقق من اختيار طلب
            bool hasSelectedOrder = ddlOrderNumbers.SelectedIndex > 0;

            // إعادة تفعيل جميع الأزرار
            btnPath1.Enabled = true;
            btnPath2.Enabled = true;
            btnPath3.Enabled = true;

            // تعطيل/تفعيل الأزرار 4,5,6 بناءً على اختيار الطلب
            btnPath4.Enabled = hasSelectedOrder;
            btnPath5.Enabled = hasSelectedOrder;
            btnPath6.Enabled = hasSelectedOrder;

            // تغيير لون الأزرار المعطلة
            if (!hasSelectedOrder)
            {
                btnPath4.CssClass = "btn path-btn disabled";
                btnPath5.CssClass = "btn path-btn disabled";
                btnPath6.CssClass = "btn path-btn disabled";
            }
            else
            {
                btnPath4.CssClass = "btn path-btn";
                btnPath5.CssClass = "btn path-btn";
                btnPath6.CssClass = "btn path-btn";
            }

            btnAutoPath.CssClass = "auto-path-btn"; // إعادة اللون الأصلي
            UncheckAllSupervisors();
        }




        // دالة عرض التنبيهات بشكل أجمل
        private void ShowAlert(string message, string icon = "warning")
        {
            string script = $@"
    Swal.fire({{
        title: 'تنبيه',
        text: '{message}',
        icon: '{icon}',
        confirmButtonText: 'حسناً',
        position: 'top-end',
        toast: true,
        timer: 3000,
        timerProgressBar: true,
        showConfirmButton: false,
        customClass: {{
            popup: 'rtl-alert',
            title: 'rtl-text',
            content: 'rtl-text'
        }}
    }});";

            ScriptManager.RegisterStartupScript(this, GetType(),
                "alert_" + Guid.NewGuid().ToString(), script, true);
        }

        private void LogError(Exception ex)
        {
            // يمكن تنفيذ آلية تسجيل الأخطاء هنا
            System.Diagnostics.Debug.WriteLine($"خطأ: {ex.Message}");
            // يمكن إضافة تسجيل في قاعدة البيانات أو ملف
        }

       

        private void UncheckAllSupervisors()
        {
            // إلغاء تحديد جميع المشرفين في checkboxContainer
            foreach (Control control in checkboxContainer.Controls)
            {
                if (control is CheckBox checkBox)
                {
                    checkBox.Checked = false;
                }
            }
        }
        private void SelectSupervisors(string supervisorsList)
        {
            // تقسيم النص إلى مصفوفة باستخدام الفاصلة المنقوطة
            string[] supervisors = supervisorsList.Split(';');

            // إلغاء تحديد جميع المشرفين أولاً
            UncheckAllSupervisors();

            // تحديد المشرفين المطلوبين
            foreach (string supervisor in supervisors)
            {
                switch (supervisor.Trim())
                {
                    case "خدمات الموظفين":
                        chk1.Checked = true;
                        break;
                    case "إدارة تخطيط الموارد البشرية":
                        chk2.Checked = true;
                        break;
                    case "إدارة تقنية المعلومات":
                        chk3.Checked = true;
                        break;
                    case "مراقبة الدوام":
                        chk4.Checked = true;
                        break;
                    case "السجلات الطبية":
                        chk5.Checked = true;
                        break;
                    case "إدارة الرواتب والاستحقاقات":
                        chk6.Checked = true;
                        break;
                    case "إدارة القانونية والالتزام":
                        chk7.Checked = true;
                        break;
                    case "خدمات الموارد البشرية":
                        chk8.Checked = true;
                        break;
                    case "إدارة الإسكان":
                        chk9.Checked = true;
                        break;
                    case "قسم الملفات":
                        chk10.Checked = true;
                        break;
                    case "العيادات الخارجية":
                        chk11.Checked = true;
                        break;
                    case "التأمينات الاجتماعية":
                        chk12.Checked = true;
                        break;
                    case "وحدة مراقبة المخزون":
                        chk13.Checked = true;
                        break;
                    case "إدارة تنمية الإيرادات":
                        chk14.Checked = true;
                        break;
                    case "إدارة الأمن و السلامة":
                        chk15.Checked = true;
                        break;
                    case "الطب الاتصالي":
                        chk16.Checked = true;
                        break;
                    default:
                        // تسجيل المشرفين غير المعروفين للتدقيق
                        System.Diagnostics.Debug.WriteLine($"مشرف غير معروف: {supervisor}");
                        break;
                }
            }
        }
        protected void btnDirectToManager_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من اختيار رقم طلب صحيح
                if (ddlOrderNumbers.SelectedIndex == 0)
                {
                    LabelError.Text = "يرجى اختيار رقم الطلب.";
                    LabelError.Visible = true;
                    LabelMessage.Visible = false;
                    return;
                }

                // التحقق من وجود تفاصيل/رقم
                string rakam = TextBoxRakam.Text.Trim();
                if (string.IsNullOrEmpty(rakam))
                {
                    LabelError.Text = "يرجى كتابة التفاصيل/الرقم.";
                    LabelError.Visible = true;
                    LabelMessage.Visible = false;
                    return;
                }

                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string confirmedBy = Session["Username"]?.ToString();
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string updateQuery = @"
                UPDATE ordersTable 
                SET [حالة الطلب] = '(D)', 
                    [تم التحويل/الإلغاء من قبل المنسق] = @ConfirmedBy, 
                    [تفاصيل المنسق] = ISNULL([تفاصيل المنسق], '') + @Rakam, 
                    [نوع التحويل] = 'مباشر ' 
                WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(updateQuery, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        cmd.Parameters.AddWithValue("@ConfirmedBy", confirmedBy);
                        cmd.Parameters.AddWithValue("@Rakam", rakam);

                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            LabelMessage.Text = "تم التحويل إلى مدير الموارد البشرية.";
                            OrderDetailsPanel.Visible = false;
                            LabelMessage.Visible = true;
                            LabelError.Visible = false;

                            PopulateOrderNumbers();
                            if (Master is SiteMaster master)
                            {
                                master.UpdateNewOrdersCount();
                            }

                            // تفريغ الحقول
                            ClearFields();
                        }
                        else
                        {
                            LabelError.Text = "حدث خطأ أثناء التحويل للمدير.";
                            LabelError.Visible = true;
                            LabelMessage.Visible = false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"=== خطأ ===");
                System.Diagnostics.Debug.WriteLine($"نوع الخطأ: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"رسالة الخطأ: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");

                LabelError.Text = "حدث خطأ أثناء معالجة الطلب: " + ex.Message;
                LabelError.Visible = true;
                LabelMessage.Visible = false;
            }
        }

        // إضافة دالة جديدة لتحميل الطلبات القابلة للاستعادة
        private void PopulateRestorableOrders()
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"
               SELECT DISTINCT 
                   [رقم الطلب], 
                   [اسم الموظف],
                   CASE 
                       WHEN [اسم الموظف] IS NULL OR [اسم الموظف] = '' 
                       THEN CAST([رقم الطلب] AS NVARCHAR(50))
                       ELSE CAST([رقم الطلب] AS NVARCHAR(50)) + ' | ' + [اسم الموظف] + ' | ' + [حالة الطلب]
                   END AS DisplayText
               FROM ordersTable 
               WHERE [حالة الطلب] IN ('(C)', N'يتطلب إجراءات من المشرف', N'أُعيد بواسطة أحد المشرفين')
               ORDER BY [رقم الطلب] DESC";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        ddlRestoreOrders.DataSource = reader;
                        ddlRestoreOrders.DataTextField = "DisplayText";
                        ddlRestoreOrders.DataValueField = "رقم الطلب";
                        ddlRestoreOrders.DataBind();
                        ddlRestoreOrders.Items.Insert(0, new ListItem("-- اختر رقم الطلب لاستعادته --", ""));
                    }
                }
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل قائمة الطلبات: " + ex.Message;
                LabelError.Visible = true;
            }
        }

        // دالة استعادة الطلب
        protected void btnRestoreOrder_Click(object sender, EventArgs e)
        {
            if (ddlRestoreOrders.SelectedIndex == 0)
            {
                LabelError.Text = "يرجى اختيار رقم الطلب.";
                LabelError.Visible = true;
                return;
            }

            string selectedOrderNumber = ddlRestoreOrders.SelectedValue;
            string notes = txtRestoreNotes.Text.Trim();
            string coordinatorUsername = Session["Username"]?.ToString();

            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    // التحقق من حالة الطلب قبل الاستعادة
                    string checkQuery = @"
                SELECT [حالة الطلب]
                FROM ordersTable 
                WHERE [رقم الطلب] = @OrderNumber";

                    string currentStatus = "";
                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, con))
                    {
                        checkCmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        con.Open();
                        var result = checkCmd.ExecuteScalar();
                        if (result != null)
                        {
                            currentStatus = result.ToString();
                        }
                    }

                    // التأكد من أن الطلب في حالة يمكن استعادتها
                    if (currentStatus == "(C)" ||
                        currentStatus == "يتطلب إجراءات من المشرف" ||
                        currentStatus == "أُعيد بواسطة أحد المشرفين")
                    {
                        string updateQuery = @"
                    UPDATE ordersTable 
                    SET [حالة الطلب] = '(B)',
                        [تم التحويل/الإلغاء من قبل المنسق] = @CoordinatorAction,
                        [تفاصيل المنسق] = ISNULL([تفاصيل المنسق], '') + @Notes
                    WHERE [رقم الطلب] = @OrderNumber";

                        using (SqlCommand cmd = new SqlCommand(updateQuery, con))
                        {
                            string coordinatorAction = $"{DateTime.Now:yyyy-MM-dd} - تمت استعادة الطلب بواسطة {coordinatorUsername}";
                            string notesWithDate = string.IsNullOrEmpty(notes) ? "" : $"\n{DateTime.Now:yyyy-MM-dd} - {notes}";

                            cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                            cmd.Parameters.AddWithValue("@CoordinatorAction", coordinatorAction);
                            cmd.Parameters.AddWithValue("@Notes", notesWithDate);

                            int rowsAffected = cmd.ExecuteNonQuery();

                            if (rowsAffected > 0)
                            {
                                // مسح حالات المشرفين باستثناء الاعتمادات
string clearSupervisorsQuery = @"
UPDATE ordersTable 
SET 
    [مشرف خدمات الموظفين] = CASE 
        WHEN [مشرف خدمات الموظفين] LIKE '%اعتماد بواسطة%' THEN [مشرف خدمات الموظفين]
        ELSE NULL 
    END,
    [مشرف إدارة تخطيط الموارد البشرية] = CASE 
        WHEN [مشرف إدارة تخطيط الموارد البشرية] LIKE '%اعتماد بواسطة%' THEN [مشرف إدارة تخطيط الموارد البشرية]
        ELSE NULL 
    END,
    [مشرف إدارة تقنية المعلومات] = CASE 
        WHEN [مشرف إدارة تقنية المعلومات] LIKE '%اعتماد بواسطة%' THEN [مشرف إدارة تقنية المعلومات]
        ELSE NULL 
    END,
    [مشرف مراقبة الدوام] = CASE 
        WHEN [مشرف مراقبة الدوام] LIKE '%اعتماد بواسطة%' THEN [مشرف مراقبة الدوام]
        ELSE NULL 
    END,
    [مشرف السجلات الطبية] = CASE 
        WHEN [مشرف السجلات الطبية] LIKE '%اعتماد بواسطة%' THEN [مشرف السجلات الطبية]
        ELSE NULL 
    END,
    [مشرف إدارة الرواتب والاستحقاقات] = CASE 
        WHEN [مشرف إدارة الرواتب والاستحقاقات] LIKE '%اعتماد بواسطة%' THEN [مشرف إدارة الرواتب والاستحقاقات]
        ELSE NULL 
    END,
    [مشرف إدارة القانونية والالتزام] = CASE 
        WHEN [مشرف إدارة القانونية والالتزام] LIKE '%اعتماد بواسطة%' THEN [مشرف إدارة القانونية والالتزام]
        ELSE NULL 
    END,
    [مشرف خدمات الموارد البشرية] = CASE 
        WHEN [مشرف خدمات الموارد البشرية] LIKE '%اعتماد بواسطة%' THEN [مشرف خدمات الموارد البشرية]
        ELSE NULL 
    END,
    [مشرف إدارة الإسكان] = CASE 
        WHEN [مشرف إدارة الإسكان] LIKE '%اعتماد بواسطة%' THEN [مشرف إدارة الإسكان]
        ELSE NULL 
    END,
    [مشرف قسم الملفات] = CASE 
        WHEN [مشرف قسم الملفات] LIKE '%اعتماد بواسطة%' THEN [مشرف قسم الملفات]
        ELSE NULL 
    END,
    [مشرف العيادات الخارجية] = CASE 
        WHEN [مشرف العيادات الخارجية] LIKE '%اعتماد بواسطة%' THEN [مشرف العيادات الخارجية]
        ELSE NULL 
    END,
    [مشرف التأمينات الاجتماعية] = CASE 
        WHEN [مشرف التأمينات الاجتماعية] LIKE '%اعتماد بواسطة%' THEN [مشرف التأمينات الاجتماعية]
        ELSE NULL 
    END,
    [مشرف وحدة مراقبة المخزون] = CASE 
        WHEN [مشرف وحدة مراقبة المخزون] LIKE '%اعتماد بواسطة%' THEN [مشرف وحدة مراقبة المخزون]
        ELSE NULL 
    END,
    [مشرف إدارة تنمية الإيرادات] = CASE 
        WHEN [مشرف إدارة تنمية الإيرادات] LIKE '%اعتماد بواسطة%' THEN [مشرف إدارة تنمية الإيرادات]
        ELSE NULL 
    END,
    [مشرف إدارة الأمن و السلامة] = CASE 
        WHEN [مشرف إدارة الأمن و السلامة] LIKE '%اعتماد بواسطة%' THEN [مشرف إدارة الأمن و السلامة]
        ELSE NULL 
    END,
    [مشرف الطب الاتصالي] = CASE 
        WHEN [مشرف الطب الاتصالي] LIKE '%اعتماد بواسطة%' THEN [مشرف الطب الاتصالي]
        ELSE NULL 
    END
WHERE [رقم الطلب] = @OrderNumber";

                                using (SqlCommand clearCmd = new SqlCommand(clearSupervisorsQuery, con))
                                {
                                    clearCmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                    clearCmd.ExecuteNonQuery();
                                }

                                LabelMessage.Text = "تم استعادة الطلب بنجاح.";
                                LabelMessage.Visible = true;
                                LabelError.Visible = false;

                                // تحديث القوائم
                                PopulateOrderNumbers();
                                PopulateRestorableOrders();
                                txtRestoreNotes.Text = "";
                            }
                        }
                    }
                    else
                    {
                        LabelError.Text = "لا يمكن استعادة هذا الطلب في حالته الحالية.";
                        LabelError.Visible = true;
                        LabelMessage.Visible = false;
                    }
                }
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء استعادة الطلب: " + ex.Message;
                LabelError.Visible = true;
                LabelMessage.Visible = false;
            }
        }

        protected void ddlRestoreOrders_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlRestoreOrders.SelectedIndex == 0)
            {
                restoreDetailsPanel.Visible = false;
                return;
            }
            try
            {
                LoadOrderDetails(ddlRestoreOrders.SelectedValue, "restore");
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل التفاصيل: " + ex.Message;
                LabelError.Visible = true;
                restoreDetailsPanel.Visible = false;
            }
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"
                SELECT TOP 1 
                    o.[حالة الطلب],
                    o.[تم التحويل/الإلغاء من قبل المنسق],
                    STUFF((
                        SELECT ', ' + SupervisorName
                        FROM (
                            SELECT 'مشرف خدمات الموظفين' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف خدمات الموظفين] IS NOT NULL AND [مشرف خدمات الموظفين] <> ''
                            UNION ALL
                            SELECT 'مشرف إدارة تخطيط الموارد البشرية' FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف إدارة تخطيط الموارد البشرية] IS NOT NULL AND [مشرف إدارة تخطيط الموارد البشرية] <> ''
                            UNION ALL
                            SELECT 'مشرف إدارة تقنية المعلومات' FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف إدارة تقنية المعلومات] IS NOT NULL AND [مشرف إدارة تقنية المعلومات] <> ''
                            UNION ALL
                            SELECT 'مشرف مراقبة الدوام' FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف مراقبة الدوام] IS NOT NULL AND [مشرف مراقبة الدوام] <> ''
                            UNION ALL
                            SELECT 'مشرف السجلات الطبية' FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف السجلات الطبية] IS NOT NULL AND [مشرف السجلات الطبية] <> ''
                            UNION ALL
                            SELECT 'مشرف إدارة الرواتب والاستحقاقات' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف إدارة الرواتب والاستحقاقات] IS NOT NULL AND [مشرف إدارة الرواتب والاستحقاقات] <> ''
                            UNION ALL
                            SELECT 'مشرف إدارة القانونية والالتزام' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف إدارة القانونية والالتزام] IS NOT NULL AND [مشرف إدارة القانونية والالتزام] <> ''
                            UNION ALL
                            SELECT 'مشرف خدمات الموارد البشرية' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف خدمات الموارد البشرية] IS NOT NULL AND [مشرف خدمات الموارد البشرية] <> ''
                            UNION ALL
                            SELECT 'مشرف إدارة الإسكان' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف إدارة الإسكان] IS NOT NULL AND [مشرف إدارة الإسكان] <> ''
                            UNION ALL
                            SELECT 'مشرف قسم الملفات' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف قسم الملفات] IS NOT NULL AND [مشرف قسم الملفات] <> ''
                            UNION ALL
                            SELECT 'مشرف العيادات الخارجية' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف العيادات الخارجية] IS NOT NULL AND [مشرف العيادات الخارجية] <> ''
                            UNION ALL
                            SELECT 'مشرف التأمينات الاجتماعية' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف التأمينات الاجتماعية] IS NOT NULL AND [مشرف التأمينات الاجتماعية] <> ''
                            UNION ALL
                            SELECT 'مشرف وحدة مراقبة المخزون' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف وحدة مراقبة المخزون] IS NOT NULL AND [مشرف وحدة مراقبة المخزون] <> ''
                            UNION ALL
                            SELECT 'مشرف إدارة تنمية الإيرادات' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف إدارة تنمية الإيرادات] IS NOT NULL AND [مشرف إدارة تنمية الإيرادات] <> ''
                            UNION ALL
                            SELECT 'مشرف إدارة الأمن و السلامة' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف إدارة الأمن و السلامة] IS NOT NULL AND [مشرف إدارة الأمن و السلامة] <> ''
                            UNION ALL
                            SELECT 'مشرف الطب الاتصالي' AS SupervisorName FROM ordersTable 
                            WHERE [رقم الطلب] = @OrderNumber AND [مشرف الطب الاتصالي] IS NOT NULL AND [مشرف الطب الاتصالي] <> ''
                        ) AS Supervisors
                        FOR XML PATH(''), TYPE
                    ).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS AssignedSupervisors
                FROM ordersTable o
                WHERE o.[رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", ddlRestoreOrders.SelectedValue);
                        con.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                lblRestoreCurrentStatus.Text = reader["حالة الطلب"].ToString();
                                lblTransferDate.Text = reader["تم التحويل/الإلغاء من قبل المنسق"]?.ToString() ?? "لا يوجد تاريخ تحويل";
                                string supervisors = reader["AssignedSupervisors"]?.ToString();
                                lblAssignedSupervisors.Text = string.IsNullOrEmpty(supervisors) ?
                                    "لا يوجد مشرفين معينين" : supervisors;

                                restoreDetailsPanel.Visible = true;
                            }
                            else
                            {
                                restoreDetailsPanel.Visible = false;
                                LabelError.Text = "لم يتم العثور على بيانات الطلب.";
                                LabelError.Visible = true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل التفاصيل: " + ex.Message;
                LabelError.Visible = true;
                restoreDetailsPanel.Visible = false;
            }
        }

        private void PopulateRestorableOrders(string searchTerm = "", string filter = "today")
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"
                    SELECT DISTINCT 
                        o.[رقم الطلب], 
                        o.[اسم الموظف],
                        o.[تاريخ الطلب],
                        o.[حالة الطلب],
                        CASE 
                            WHEN o.[اسم الموظف] IS NULL OR o.[اسم الموظف] = '' 
                            THEN CAST(o.[رقم الطلب] AS NVARCHAR(50))
                            ELSE CAST(o.[رقم الطلب] AS NVARCHAR(50)) + ' | ' + o.[اسم الموظف]
                        END AS DisplayText
                    FROM ordersTable o
                    WHERE ([حالة الطلب] = '(C)' 
                           OR [حالة الطلب] = N'يتطلب إجراءات من المشرف'
                           OR [حالة الطلب] = N'أُعيد بواسطة أحد المشرفين')
                    AND (
                        @SearchTerm = '' 
                        OR CAST([رقم الطلب] AS NVARCHAR) LIKE @SearchTerm + '%'
                        OR [اسم الموظف] LIKE N'%' + @SearchTerm + '%'
                    )
                    AND (
                        @Filter = 'all'
                        OR (@Filter = 'today' AND CONVERT(date, [تاريخ الطلب]) = CONVERT(date, GETDATE()))
                        OR (@Filter = 'week' AND DATEDIFF(day, [تاريخ الطلب], GETDATE()) <= 7)
                        OR (@Filter = 'month' AND DATEDIFF(month, [تاريخ الطلب], GETDATE()) = 0)
                    )
                    ORDER BY [تاريخ الطلب] DESC, [رقم الطلب] DESC";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@SearchTerm", searchTerm ?? "");
                        cmd.Parameters.AddWithValue("@Filter", filter);

                        con.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            ddlRestoreOrders.Items.Clear();
                            ddlRestoreOrders.DataSource = reader;
                            ddlRestoreOrders.DataTextField = "DisplayText";
                            ddlRestoreOrders.DataValueField = "رقم الطلب";
                            ddlRestoreOrders.DataBind();
                        }
                    }

                    // إضافة العنصر الافتراضي مع إحصائيات
                    int resultCount = ddlRestoreOrders.Items.Count;
                    string filterText;
                    switch (filter)
                    {
                        case "today":
                            filterText = "اليوم";
                            break;
                        case "week":
                            filterText = "الأسبوع";
                            break;
                        case "month":
                            filterText = "الشهر";
                            break;
                        default:
                            filterText = "";
                            break;
                    }

                    string defaultText = resultCount > 0
                        ? filter == "all"
                            ? $"-- اختر من {resultCount} طلب --"
                            : $"-- اختر من {resultCount} طلب ({filterText}) --"
                        : "-- لا توجد طلبات --";

                    ddlRestoreOrders.Items.Insert(0, new ListItem(defaultText, ""));
                }
            }
            catch (Exception ex)
            {
                ShowError("حدث خطأ أثناء تحميل قائمة الطلبات: " + ex.Message);
            }
        }

        protected void txtSearch_TextChanged(object sender, EventArgs e)
        {
            PopulateRestorableOrders(txtSearch.Text.Trim(), ViewState["CurrentFilter"]?.ToString() ?? "today");
        }

        protected void btnFilter_Click(object sender, EventArgs e)
        {
            LinkButton btn = (LinkButton)sender;
            string filter = btn.CommandArgument;

            // تحديث حالة الأزرار
            btnFilterToday.CssClass = "btn btn-primary flex-grow-1" + (filter == "today" ? " active" : "");
            btnFilterWeek.CssClass = "btn btn-primary flex-grow-1" + (filter == "week" ? " active" : "");
            btnFilterMonth.CssClass = "btn btn-primary flex-grow-1" + (filter == "month" ? " active" : "");
            btnFilterAll.CssClass = "btn btn-primary flex-grow-1" + (filter == "all" ? " active" : "");

            ViewState["CurrentFilter"] = filter;
            PopulateRestorableOrders(txtSearch.Text.Trim(), filter);
        }


        protected void btnToggleRestoreSection_Click(object sender, EventArgs e)
        {
            restoreSectionPanel.Visible = !restoreSectionPanel.Visible;
        }



    }

}