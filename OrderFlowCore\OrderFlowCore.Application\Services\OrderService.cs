using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Core.Exceptions;
using System.Linq;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Services
{
    public class OrderService : IOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEnvironmentService _env;
        private readonly OrderServiceOptions _options;
        private readonly IFileService _fileService;
        private readonly ILogger<OrderService> _logger;

        public OrderService(
            IUnitOfWork unitOfWork,
            IEnvironmentService env,
            IOptions<OrderServiceOptions> options,
            IFileService fileService,
            ILogger<OrderService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _env = env ?? throw new ArgumentNullException(nameof(env));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<DropdownDataDto>> GetDropdownDataAsync()
        {
            try
            {

                var dropdownData = new DropdownDataDto();

                // Load data from repositories with error handling
                var departments = await _unitOfWork.Departments.GetAllAsync();
                var orderTypes = await _unitOfWork.OrdersTypes.GetAllAsync();
                var jobTypes = await _unitOfWork.JobTypes.GetAllAsync();
                var nationalities = await _unitOfWork.Nationalities.GetAllAsync();
                var employmentTypes = await _unitOfWork.EmploymentTypes.GetAllAsync();
                var qualifications = await _unitOfWork.Qualifications.GetAllAsync();

                // Convert data to DropdownItemDto
                dropdownData.Departments = departments.Select(d => new DropdownItemDto { Value = d.Name, Text = d.Name }).ToList();
                dropdownData.OrderTypes = orderTypes.Select(ot => new DropdownItemDto { Value = ot.Name, Text = ot.Name }).ToList();
                dropdownData.JobTitles = jobTypes.Select(jt => new DropdownItemDto { Value = jt.Name, Text = jt.Name }).ToList();
                dropdownData.Nationalities = nationalities.Select(n => new DropdownItemDto { Value = n.Name, Text = n.Name }).ToList();
                dropdownData.EmploymentTypes = employmentTypes.Select(et => new DropdownItemDto { Value = et.Name, Text = et.Name }).ToList();
                dropdownData.Qualifications = qualifications.Select(q => new DropdownItemDto { Value = q.Name, Text = q.Name }).ToList();

                return ServiceResult<DropdownDataDto>.Success(dropdownData);
            }
            catch (Exception ex)
            {
                return ServiceResult<DropdownDataDto>.Failure($"خطأ في تحميل بيانات القوائم المنسدلة: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderSummaryDto>> CreateOrderAsync(OrderNewDto orderDto)
        {
            try
            {

                // Validate input
                var validationResult = ValidateOrderDto(orderDto);
                if (!validationResult.IsSuccess)
                {
                    return ServiceResult<OrderSummaryDto>.Failure(validationResult.Message, validationResult.Errors);
                }

                var order = new OrdersTable
                {
                    EmployeeName = orderDto.EmployeeName.Trim(),
                    JobTitle = orderDto.JobTitle.Trim(),
                    EmployeeNumber = orderDto.EmployeeNumber.Trim(),
                    CivilRecord = orderDto.CivilRecord.Trim(),
                    Nationality = orderDto.Nationality.Trim(),
                    MobileNumber = orderDto.MobileNumber.Trim(),
                    Department = orderDto.Department.Trim(),
                    EmploymentType = orderDto.EmploymentType.Trim(),
                    Qualification = orderDto.Qualification.Trim(),
                    OrderType = orderDto.OrderType.Trim(),
                    Details = orderDto.Details?.Trim() ?? string.Empty,
                    CreatedAt = DateTime.UtcNow,
                    OrderStatus = OrderStatus.DM,
                };

                // Use FileService for attachments
                var attachmentResult = await _fileService.UploadFilesAsync(orderDto.Attachments, $"order_{orderDto.CivilRecord}");
                if (!attachmentResult.IsSuccess)
                {
                    return ServiceResult<OrderSummaryDto>.Failure(attachmentResult.Message);
                }

                for (int i = 0; i < attachmentResult.Data.Count && i < _options.MaxAttachments; i++)
                {
                    var url = attachmentResult.Data[i];
                    switch (i)
                    {
                        case 0: order.File1Url = url; break;
                        case 1: order.File2Url = url; break;
                        case 2: order.File3Url = url; break;
                        case 3: order.File4Url = url; break;
                    }
                }

                await _unitOfWork.Orders.AddAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult<OrderSummaryDto>.Success(new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }, "تم إنشاء الطلب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<OrderSummaryDto>.Failure($"خطأ في إنشاء الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Getting order details for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    _logger.LogWarning("Invalid order ID provided: {OrderId}", orderId);
                    throw new ValidationException("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    _logger.LogWarning("Order not found: {OrderId}", orderId);
                    throw new OrderNotFoundException(orderId);
                }

                var attachments = BuildAttachmentList(order);
                var orderDetails = OrderDetailsDto.FromDomain(order);

                _logger.LogInformation("Successfully retrieved order details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderDetailsDto>.Success(orderDetails);
            }
            catch (BusinessLogicException)
            {
                throw; // Re-throw business logic exceptions
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderDetailsDto>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> UploadAttachmentAsync(int orderId, byte[] fileData, string fileName)
        {
            try
            {

                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (fileData == null || fileData.Length == 0)
                {
                    return ServiceResult.Failure("بيانات الملف فارغة");
                }

                if (string.IsNullOrWhiteSpace(fileName))
                {
                    return ServiceResult.Failure("اسم الملف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Use FileService for upload
                var uploadResult = await _fileService.UploadFileAsync(fileData, fileName, $"order_{order.CivilRecord}");
                if (!uploadResult.IsSuccess)
                {
                    return ServiceResult.Failure(uploadResult.Message);
                }
                var fileUrl = uploadResult.Data;

                // Update the first available file URL
                if (string.IsNullOrEmpty(order.File1Url)) order.File1Url = fileUrl;
                else if (string.IsNullOrEmpty(order.File2Url)) order.File2Url = fileUrl;
                else if (string.IsNullOrEmpty(order.File3Url)) order.File3Url = fileUrl;
                else if (string.IsNullOrEmpty(order.File4Url)) order.File4Url = fileUrl;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم رفع المرفق بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في رفع المرفق: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> DownloadAttachmentsZipAsync(int orderId)
        {
            try
            {

                if (orderId <= 0)
                {
                    return ServiceResult<byte[]>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
                }

                var fileUrls = new List<string>();
                if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);

                if (fileUrls.Count == 0)
                {
                    return ServiceResult<byte[]>.Failure("لا توجد مرفقات");
                }

                // Use FileService for zipping
                var zipResult = await _fileService.DownloadFilesZipAsync(fileUrls);
                if (!zipResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(zipResult.Message);
                }

                return ServiceResult<byte[]>.Success(zipResult.Data);
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"خطأ في تحميل المرفقات: {ex.Message}");
            }
        }


        // DirectManager specific methods
        public async Task<ServiceResult<List<OrderSummaryDto>>> GetPendingOrdersForDirectMangerAsync()
        {
            try
            {
                // Get orders that are pending manager approval
                var pendingOrders = await _unitOfWork.Orders.GetPendingOrdersForDirectMangerAsync();

                var orderSummaries = pendingOrders.Select(order => new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<OrderSummaryDto>>.Failure($"خطأ في جلب الطلبات المعلقة: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ConfirmOrderByDirectManagerAsync(int orderId, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }


                var department = await _unitOfWork.Departments.GetByNameAsync(order.Department);
                var assistantManagerId = department?.AssistantManagerId ?? AssistantManagerType.Unknown;

                // تحديد المسار والحالة الجديدة
                OrderStatus newStatus = assistantManagerId.ToOrderStatus();
                string statusMessage = assistantManagerId == AssistantManagerType.B ? 
                    "تم تأكيد الطلب بنجاح وتحويله إلى منسق الموارد البشرية" : "تم تأكيد الطلب بنجاح وتحويله إلى مساعد المدير";
                
                
                // Update order status
                order.OrderStatus = newStatus;
                order.ConfirmedByDepartmentManager = $"{DateTime.Now:yyyy-MM-dd} | اعتماد بواسطة {userName}";

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success(statusMessage);

            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في تأكيد الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> RejectOrderByDirectManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإلغاء مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }


                // Update order status
                order.ConfirmedByDepartmentManager = $"تم الالغاء بواسطة: {userName}";
                order.ReasonForCancellation = reason;
                order.OrderStatus = OrderStatus.CancelledByDepartmentManager;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إلغاء الطلب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إلغاء الطلب: {ex.Message}");
            }
        }



        // AssistantManager specific methods
        public async Task<ServiceResult<List<OrderSummaryDto>>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId)
        {
            try
            {
                if (assistantManagerId == AssistantManagerType.Unknown)
                {
                    return ServiceResult<List<OrderSummaryDto>>.Failure("معرف مساعد المدير مطلوب");
                }

                // Get orders that are assigned to this assistant manager
                var assistantManagerOrders = await _unitOfWork.Orders.GetAssistantManagerOrdersAsync(assistantManagerId);

                var orderSummaries = assistantManagerOrders.Select(order => new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<OrderSummaryDto>>.Failure($"خطأ في جلب طلبات مساعد المدير: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ConfirmOrderByAssistantManagerAsync(int orderId, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                order.ConfirmedByAssistantManager = $"{DateTime.Now:yyyy-MM-dd} | اعتماد بواسطة {userName}";
                order.OrderStatus = OrderStatus.B;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم تأكيد الطلب وتحويله إلى منسق الموارد البشرية بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في تأكيد الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ReturnOrderToDirectManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإعادة مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                
                order.OrderStatus = OrderStatus.ReturnedByAssistantManager;
                order.ConfirmedByAssistantManager = $"{DateTime.Now:yyyy-MM-dd} | تمت الإعادة من مساعد المدير: {userName}";
                order.ReasonForCancellation = reason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إعادة الطلب إلى مدير القسم بنجاح");

            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إعادة الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> RejectOrderByAssistantManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإلغاء مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                string rejectedBy = userName;

                order.OrderStatus = OrderStatus.CancelledByAssistantManager;
                order.ConfirmedByAssistantManager = $"تم الالغاء بواسطة: {userName}";
                order.ReasonForCancellation = reason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إلغاء الطلب بنجاح");

            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إلغاء الطلب: {ex.Message}");
            }
        }


        public async Task<List<RestorableOrderDto>> GetRestorableOrdersAsync(string searchTerm = "", string filter = "today")
        {
            var statuses = new[] { "(C)", "يتطلب إجراءات من المشرف", "أُعيد بواسطة أحد المشرفين" };
            var orders = await _unitOfWork.Orders.GetOrdersByStatusesAsync(statuses);
            var filtered = orders.Where(o =>
                (string.IsNullOrEmpty(searchTerm) ||
                 o.Id.ToString().StartsWith(searchTerm) ||
                 (o.EmployeeName != null && o.EmployeeName.Contains(searchTerm)))
            );
            if (filter == "today")
            {
                filtered = filtered.Where(o => o.CreatedAt.Date == DateTime.Today);
            }
            else if (filter == "week")
            {
                filtered = filtered.Where(o => (DateTime.Today - o.CreatedAt.Date).TotalDays <= 7);
            }
            else if (filter == "month")
            {
                filtered = filtered.Where(o => o.CreatedAt.Month == DateTime.Today.Month && o.CreatedAt.Year == DateTime.Today.Year);
            }
            // else 'all' returns all
            return filtered
                .OrderByDescending(o => o.CreatedAt)
                .ThenByDescending(o => o.Id)
                .Select(o => new RestorableOrderDto
                {
                    OrderNumber = o.Id.ToString(),
                    EmployeeName = o.EmployeeName,
                    OrderDate = o.CreatedAt.ToString("yyyy-MM-dd"),
                    OrderStatus = o.OrderStatus,
                    DisplayText = string.IsNullOrEmpty(o.EmployeeName) ? o.Id.ToString() : o.Id + " | " + o.EmployeeName
                })
                .ToList();
        }

        public async Task<RestoreDetailsDto> GetRestoreDetailsAsync(int orderId)
        {
            // Assume repository has a method to get order by number and supervisor columns
            var order = await _unitOfWork.Orders.GetOrderByIdAsync(orderId);
            if (order == null) return null;

            // Current status and transfer date
            var currentStatus = order.OrderStatus;
            string transferDate = order.ConfirmedByCoordinator; // or another field as needed

            // Assigned supervisors: concatenate supervisor columns that are not null/empty
            var supervisorNames = new[] {
                "مشرف خدمات الموظفين",
                "مشرف إدارة تخطيط الموارد البشرية",
                "مشرف إدارة تقنية المعلومات",
                "مشرف مراقبة الدوام",
                "مشرف السجلات الطبية",
                "مشرف إدارة الرواتب والاستحقاقات",
                "مشرف إدارة القانونية والالتزام",
                "مشرف خدمات الموارد البشرية",
                "مشرف إدارة الإسكان",
                "مشرف قسم الملفات",
                "مشرف العيادات الخارجية",
                "مشرف التأمينات الاجتماعية",
                "مشرف وحدة مراقبة المخزون",
                "مشرف إدارة تنمية الإيرادات",
                "مشرف إدارة الأمن و السلامة",
                "مشرف الطب الاتصالي"
            };
            //var assigned = supervisorNames
            //    .Where(name => order.SupervisorStatuses.ContainsKey(name) && !string.IsNullOrEmpty(order.SupervisorStatuses[name]))
            //    .Select(name => name)
            //    .ToList();
            string assignedSupervisors = supervisorNames.Count() > 0 ? string.Join(", ", supervisorNames) : "لا يوجد مشرفين معينين";

            return new RestoreDetailsDto
            {
                CurrentStatus = currentStatus,
                TransferDate = transferDate,
                AssignedSupervisors = assignedSupervisors
            };
        }

        public async Task<ServiceResult> RestoreOrderAsync(int orderId, string notes, string coordinatorUsername)
        {
            var order = await _unitOfWork.Orders.GetOrderByIdAsync(orderId);
            if (order == null)
                return ServiceResult.Failure("لم يتم العثور على الطلب.");

            // Check if order is in a restorable state
            if (order.OrderStatus != OrderStatus.C && order.OrderStatus != OrderStatus.ActionRequiredBySupervisor && order.OrderStatus != OrderStatus.ReturnedBySupervisor)
                return ServiceResult.Failure("لا يمكن استعادة هذا الطلب في حالته الحالية.");

            // Update order fields
            order.OrderStatus = OrderStatus.B;
            string coordinatorAction = $"{DateTime.Now:yyyy-MM-dd} - تمت استعادة الطلب بواسطة {coordinatorUsername}";
            string notesWithDate = string.IsNullOrEmpty(notes) ? "" : $"\n{DateTime.Now:yyyy-MM-dd} - {notes}";
            order.ConfirmedByCoordinator = coordinatorAction;
            order.CoordinatorDetails = (order.CoordinatorDetails ?? "") + notesWithDate;

            // Clear supervisor statuses except approvals
            //foreach (var key in order.SupervisorStatuses.Keys.ToList())
            //{
            //    var value = order.SupervisorStatuses[key];
            //    if (string.IsNullOrEmpty(value) || !value.Contains("اعتماد بواسطة"))
            //        order.SupervisorStatuses[key] = null;
            //}

            await _unitOfWork.Orders.UpdateAsync(order);
            return ServiceResult.Success();
        }

        // تحويل الطلب
        public async Task<ServiceResult> SubmitOrderAsync(int orderId, string details, string username, List<string> checkedSupervisors)
        {
            if (orderId == 0)
                return ServiceResult.Failure("يرجى اختيار رقم الطلب.");
            if (string.IsNullOrWhiteSpace(details))
                return ServiceResult.Failure("يرجى كتابة التفاصيل/الرقم.");
            if (checkedSupervisors == null || checkedSupervisors.Count == 0)
                return ServiceResult.Failure("يجب اختيار قسم على الأقل للاعتماد.");

            var order = await _unitOfWork.Orders.GetOrderByIdAsync(orderId);
            if (order == null)
                return ServiceResult.Failure("لم يتم العثور على الطلب.");

            // Update order fields
            order.OrderStatus = OrderStatus.C;
            order.ConfirmedByCoordinator = username;
            order.CoordinatorDetails = (order.CoordinatorDetails ?? "") + details;
            order.ReasonForCancellation = string.Empty;
            order.TransferType = "يدوي";

            // Update supervisor statuses
            foreach (var supervisor in checkedSupervisors)
            {
                string columnName = "مشرف " + supervisor;
                //if (order.SupervisorStatuses.ContainsKey(columnName))
                //{
                //    var currentValue = order.SupervisorStatuses[columnName];
                //    if (string.IsNullOrEmpty(currentValue) || currentValue == "/" || currentValue.StartsWith("تم الإعادة"))
                //    {
                //        order.SupervisorStatuses[columnName] = $"{DateTime.Now:yyyy-MM-dd} الطلب تحت التنفيذ";
                //    }
                //}
            }

            await _unitOfWork.Orders.UpdateAsync(order);
            return ServiceResult.Success("تم تحويل الطلب للاعتماد بنجاح.");
        }

        // يتطلب إجراءات
        public async Task<ServiceResult> MarkOrderNeedsActionAsync(int orderId, string actionDetails, string username)
        {
            if (orderId == 0)
                return ServiceResult.Failure("يرجى اختيار رقم الطلب.");
            if (string.IsNullOrWhiteSpace(actionDetails))
                return ServiceResult.Failure("يرجى إدخال الإجراءات المطلوبة.");

            var order = await _unitOfWork.Orders.GetOrderByIdAsync(orderId);
            if (order == null)
                return ServiceResult.Failure("لم يتم العثور على الطلب.");

            order.OrderStatus = OrderStatus.ActionRequired;
            order.ConfirmedByCoordinator = username;
            order.SupervisorNotes = actionDetails;

            await _unitOfWork.Orders.UpdateAsync(order);
            return ServiceResult.Success("تم حفظ طلب الإجراءات بنجاح.");
        }

        // إعادة الطلب
        public async Task<ServiceResult> ReturnOrderAsync(int orderId, string rejectReason, string username)
        {
            if (orderId == 0)
                return ServiceResult.Failure("يرجى اختيار رقم الطلب.");
            if (string.IsNullOrWhiteSpace(rejectReason))
                return ServiceResult.Failure("يرجى إدخال سبب الإعادة.");

            var order = await _unitOfWork.Orders.GetOrderByIdAsync(orderId);
            if (order == null)
                return ServiceResult.Failure("لم يتم العثور على الطلب.");

            // For now, always set to (DM) as in the simple case
            order.OrderStatus = OrderStatus.DM;
            order.ConfirmedByCoordinator = $"{DateTime.Now:yyyy-MM-dd} | تم الإعادة بواسطة {username}";
            order.ReasonForCancellation = rejectReason;

            await _unitOfWork.Orders.UpdateAsync(order);
            return ServiceResult.Success("تم إعادة الطلب بنجاح.");
        }

        // إلغاء الطلب
        public async Task<ServiceResult> RejectOrderAsync(int orderId, string rejectReason, string username)
        {
            try
            {
                _logger.LogInformation("Rejecting order {Id} by user {Username}", orderId, username);

                if (orderId == 0)
                {
                    return ServiceResult.Failure("رقم الطلب مطلوب");
                }

                if (string.IsNullOrWhiteSpace(rejectReason))
                {
                    return ServiceResult.Failure("سبب الإلغاء مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Update order status to rejected
                order.OrderStatus = OrderStatus.CancelledByManager;
                order.ReasonForCancellation = rejectReason.Trim();
                order.ConfirmedByCoordinator = $"{DateTime.Now:yyyy-MM-dd} | تم الإلغاء بواسطة {username}";
                
                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Order {Id} rejected successfully by {Username}", orderId, username);
                return ServiceResult.Success("تم إلغاء الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting order {Id} by user {Username}", orderId, username);
                return ServiceResult.Failure($"خطأ في إلغاء الطلب: {ex.Message}");
            }
        }

        // التوجيه التلقائي
        public async Task<ServiceResult> ApplyAutoPathAsync(int orderId, string details, string username)
        {
            if (orderId == 0)
                return ServiceResult.Failure("يرجى اختيار رقم الطلب.");
            if (string.IsNullOrWhiteSpace(details))
                return ServiceResult.Failure("يرجى كتابة التفاصيل/الرقم.");

            var order = await _unitOfWork.Orders.GetOrderByIdAsync(orderId);
            if (order == null)
                return ServiceResult.Failure("لم يتم العثور على الطلب.");

            // Find auto path (simulate logic; in real code, query AutoRouting table)
            //var autoSupervisors = order.AutoPathSupervisors; // Assume this is set by repository or another service
            //if (autoSupervisors == null || autoSupervisors.Count() == 0)
            //    return ServiceResult.Failure("لم يتم العثور على مسار مناسب لهذا النوع من الطلبات.");

            // Update order fields
            order.OrderStatus = OrderStatus.C;
            order.ConfirmedByCoordinator = username;
            order.CoordinatorDetails = (order.CoordinatorDetails ?? "") + details;
            order.TransferType = "تلقائي";

            // Update supervisor statuses for autoSupervisors
            //foreach (var supervisor in autoSupervisors)
            //{
            //    string columnName = "مشرف " + supervisor;
            //    if (order.SupervisorStatuses.ContainsKey(columnName))
            //    {
            //        var currentValue = order.SupervisorStatuses[columnName];
            //        if (string.IsNullOrEmpty(currentValue) || currentValue == "/" || currentValue.StartsWith("تم الإعادة"))
            //        {
            //            order.SupervisorStatuses[columnName] = $"{DateTime.Now:yyyy-MM-dd} الطلب تحت التنفيذ";
            //        }
            //    }
            //}

            await _unitOfWork.Orders.UpdateAsync(order);
            return ServiceResult.Success("تم التوجيه التلقائي بنجاح.");
        }

        // تحويل للمدير
        public async Task<ServiceResult> DirectToManagerAsync(int orderId, string details, string username)
        {
            if (orderId == 0)
                return ServiceResult.Failure("يرجى اختيار رقم الطلب.");
            if (string.IsNullOrWhiteSpace(details))
                return ServiceResult.Failure("يرجى كتابة التفاصيل/الرقم.");

            var order = await _unitOfWork.Orders.GetOrderByIdAsync(orderId);
            if (order == null)
                return ServiceResult.Failure("لم يتم العثور على الطلب.");

            order.OrderStatus = OrderStatus.D;
            order.ConfirmedByCoordinator = username;
            order.CoordinatorDetails = (order.CoordinatorDetails ?? "") + details;
            order.TransferType = "مباشر ";

            await _unitOfWork.Orders.UpdateAsync(order);
            return ServiceResult.Success("تم تحويل الطلب للمدير بنجاح.");
        }
        
        

        public async Task<ServiceResult<List<OrderPrintListItemDto>>> GetPrintableOrdersAsync(string searchTerm, string filter)
        {
            try
            {
                _logger.LogInformation("Getting printable orders with search term: {SearchTerm}, filter: {Filter}", searchTerm, filter);

                var orders = await _unitOfWork.Orders.GetAllAsync();

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    orders = orders.Where(o =>
                        o.EmployeeName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        o.EmployeeNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        o.CivilRecord.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                    ).ToList();
                }

                // Apply date filter
                var today = DateTime.Today;
                switch (filter?.ToLower())
                {
                    case "today":
                        orders = orders.Where(o => o.CreatedAt.Date == today).ToList();
                        break;
                    case "week":
                        var weekStart = today.AddDays(-(int)today.DayOfWeek);
                        orders = orders.Where(o => o.CreatedAt.Date >= weekStart).ToList();
                        break;
                    case "month":
                        orders = orders.Where(o => o.CreatedAt.Month == today.Month && o.CreatedAt.Year == today.Year).ToList();
                        break;
                    case "all":
                    default:
                        // No additional filtering
                        break;
                }

                var result = orders.Select(o => new OrderPrintListItemDto
                {
                    OrderId = o.Id,
                    OrderNumber = o.Id.ToString(),
                    EmployeeName = o.EmployeeName
                }).ToList();

                return ServiceResult<List<OrderPrintListItemDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting printable orders");
                return ServiceResult<List<OrderPrintListItemDto>>.Failure($"خطأ في جلب الطلبات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderPrintDetailsDto>> GetOrderPrintDetailsAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Getting order print details for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    return ServiceResult<OrderPrintDetailsDto>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<OrderPrintDetailsDto>.Failure("لم يتم العثور على الطلب");
                }

                var details = new OrderPrintDetailsDto
                {
                    OrderId = order.Id,
                    OrderNumber = order.Id.ToString(),
                    OrderDate = order.CreatedAt.ToString("yyyy-MM-dd"),
                    OrderStatus = order.OrderStatus.ToDisplayString(),
                    OrderType = order.OrderType,
                    EmployeeName = order.EmployeeName,
                    Department = order.Department,
                    Notes = order.Details ?? string.Empty,
                    JobTitle = order.JobTitle,
                    EmployeeNumber = order.EmployeeNumber,
                    CivilRegistry = order.CivilRecord,
                    Nationality = order.Nationality,
                    MobileNumber = order.MobileNumber,
                    EmploymentType = order.EmploymentType,
                    Qualification = order.Qualification,
                    ManagerApproval = order.ConfirmedByDepartmentManager ?? "-",
                    SupervisorApproval = order.SupervisorNotes ?? "-",
                    CoordinatorApproval = order.ConfirmedByCoordinator ?? "-",
                    CancellationReason = order.ReasonForCancellation ?? "-",
                    CoordinatorDetails = order.CoordinatorDetails ?? "-",
                    HRManagerApproval = order.ConfirmedByAssistantManager ?? "-"
                };

                return ServiceResult<OrderPrintDetailsDto>.Success(details);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order print details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderPrintDetailsDto>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> DownloadOrderAttachmentsZipAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Downloading attachments for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    return ServiceResult<byte[]>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
                }

                var fileUrls = new List<string>();
                if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);

                if (!fileUrls.Any())
                {
                    return ServiceResult<byte[]>.Failure("لا توجد مرفقات لهذا الطلب");
                }

                // Use FileService to create ZIP
                var zipResult = await _fileService.DownloadFilesZipAsync(fileUrls);
                if (!zipResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(zipResult.Message);
                }

                return ServiceResult<byte[]>.Success(zipResult.Data, "تم تحميل المرفقات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading attachments for order ID: {OrderId}", orderId);
                return ServiceResult<byte[]>.Failure($"خطأ في تحميل المرفقات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> GenerateOrderPdfAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Generating PDF for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    return ServiceResult<byte[]>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
                }

                // Use FileService to generate PDF
                var pdfResult = await _fileService.GenerateOrderPdfAsync(order);
                if (!pdfResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(pdfResult.Message);
                }

                return ServiceResult<byte[]>.Success(pdfResult.Data, "تم إنشاء ملف PDF بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating PDF for order ID: {OrderId}", orderId);
                return ServiceResult<byte[]>.Failure($"خطأ في إنشاء ملف PDF: {ex.Message}");
            }
        }

        #region Private Helper Methods

        private ServiceResult ValidateOrderDto(OrderNewDto orderDto)
        {
            var errors = new List<string>();

            if (orderDto == null)
            {
                return ServiceResult.Failure("بيانات الطلب مطلوبة");
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(orderDto.EmployeeName))
                errors.Add("اسم الموظف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.JobTitle))
                errors.Add("الوظيفة مطلوبة");

            if (string.IsNullOrWhiteSpace(orderDto.EmployeeNumber))
                errors.Add("رقم الموظف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.CivilRecord))
                errors.Add("السجل المدني مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Nationality))
                errors.Add("الجنسية مطلوبة");

            if (string.IsNullOrWhiteSpace(orderDto.MobileNumber))
                errors.Add("رقم الجوال مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Department))
                errors.Add("القسم مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.EmploymentType))
                errors.Add("نوع التوظيف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Qualification))
                errors.Add("المؤهل مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.OrderType))
                errors.Add("نوع الطلب مطلوب");

            // Validate mobile number format
            if (!string.IsNullOrWhiteSpace(orderDto.MobileNumber) &&
                !Regex.IsMatch(orderDto.MobileNumber, @"^[0-9+\-\s()]+$"))
            {
                errors.Add("صيغة رقم الجوال غير صحيحة");
            }

            // Validate attachments count
            if (orderDto.Attachments?.Count > _options.MaxAttachments)
            {
                errors.Add($"الحد الأقصى للمرفقات هو {_options.MaxAttachments}");
            }

            return errors.Any()
                ? ServiceResult.Failure("فشل في التحقق من صحة البيانات", errors)
                : ServiceResult.Success();
        }

        private List<AttachmentDto> BuildAttachmentList(OrdersTable order)
        {
            var attachments = new List<AttachmentDto>();

            if (!string.IsNullOrEmpty(order.File1Url))
                attachments.Add(new AttachmentDto { FileName = "ملف 1", Uploaded = true, FileUrl = order.File1Url });
            if (!string.IsNullOrEmpty(order.File2Url))
                attachments.Add(new AttachmentDto { FileName = "ملف 2", Uploaded = true, FileUrl = order.File2Url });
            if (!string.IsNullOrEmpty(order.File3Url))
                attachments.Add(new AttachmentDto { FileName = "ملف 3", Uploaded = true, FileUrl = order.File3Url });
            if (!string.IsNullOrEmpty(order.File4Url))
                attachments.Add(new AttachmentDto { FileName = "ملف 4", Uploaded = true, FileUrl = order.File4Url });

            return attachments;
        }

        #endregion
    }
}