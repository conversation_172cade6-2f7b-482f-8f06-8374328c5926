<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="B.aspx.cs" Inherits="abozyad.WebForm5" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <style>
        /* تنسيق زر اعادة */
        .return-button {
            background-color: #ff9400;
            border-style: none;
            border-color: inherit;
            border-width: medium;
            padding: 12px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s ease;
            margin-top: 15px;
            color:white;
        }

            /* تأثير عند مرور الماوس على الأزرار */
            .return-button:hover, .submit-button:hover {
            background-color: #bd6301;
            }

                    /* تنسيق زر يتطلب إجراءات */
        .action-button {
            background-color: #007bff;
            border-style: none;
            border-color: inherit;
            border-width: medium;
            padding: 12px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s ease;
            margin-top: 15px;
            color:white;
        }

            /* تأثير عند مرور الماوس على الأزرار */
            .action-button:hover, .submit-button:hover {
            background-color: #0056b3;
            }
            .resize-enabled {
    resize: both; /* يتيح تغيير الحجم في الاتجاهين */
    overflow: auto; /* لضمان ظهور شريط التمرير إذا لزم الأمر */
}

        .form-container {
            max-width: 1500px;
            margin: 0 auto;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            font-family: Arial, sans-serif;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: space-between;
        }

        .textbox1, .btn-container {
            flex: 1;
        }

        .form-header {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        .checkbox-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            max-width: 1200px;
        }

            .checkbox-table th, .checkbox-table td {
                padding: 12px;
                text-align: right;
                vertical-align: top;
            }

        .btn-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
        }

        .submit-button, .reject-button {
            width: auto;
            padding: 12px 20px
        }

        .btn {
            border: none;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            background-color: gray;
            color: white;
        }

        .btnok {
            border: none;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            background-color: dodgerblue;
            color: white;
        }

        .btn:hover {
            background-color: #45a049;
        }

        .custom-dropdown {
            padding: 10px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
        }

            .custom-dropdown:focus {
                border-color: #4CAF50;
                outline: none;
            }

        .checkbox-container {
            display: flex;
            align-items: center;
        }

            .checkbox-container input[type="checkbox"] {
                margin-left: 8px;
            }

        .submit-button, .reject-button {
            border-style: none;
            border-color: inherit;
            border-width: medium;
            padding: 12px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s ease;
        }

        .submit-button {
            margin-bottom: 15px;
            background-color: #4CAF50;
            color: white;
        }

            .submit-button:hover {
                background-color: #45a049;
            }

        .reject-button {
            background-color: red;
            color: white;
        }

            .reject-button:hover {
                background-color: #dd0000;
            }

        .textbox1 {
            width: 100%;
            max-width: 750px;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }

        .order-details {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

            .order-details h3 {
                margin-top: 0;
            }

            .order-details p {
                margin: 5px 0;
            }

        /* تنظيم واحد لخصائص الجدول */
        .details-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-bottom: 20px;
            max-width: 1500px; /* يمكنك زيادة الحد الأقصى للعرض حسب الحاجة */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
            border: 2px solid #007bff;
        }

            .details-table th {
                background-color: #f2f2f2;
                color: #333;
                font-weight: bold;
                font-size: 18px;
                border-right: 1px solid #ddd;
            }

            .details-table th,
            .details-table td {
                padding: 16px;
                text-align: center; /* توسيط النص داخل الخلايا */
                vertical-align: middle; /* توسيط النص عموديًا */
                border: 1px solid #ddd; /* إضافة حواف للخلايا */
            }

            .details-table th {
                background-color: #f2f2f2;
                color: #333;
                font-weight: bold;
                border-right: 1px solid #ddd;
            }

            .details-table td {
                font-size: 16px;
                color: #333;
                background-color: #ffffff;
            }

            .details-table tr:nth-child(even) td {
                background-color: #ffffff;
            }

            .details-table tr:hover td {
                background-color: #e9e9e9;
            }

            .details-table td:first-child {
                border-left: 1px solid #ddd; /* تأكد من وجود الحافة اليسرى */
            }

            .details-table td:last-child {
                border-right: 1px solid #ddd; /* تأكد من وجود الحافة اليمنى */
            }

            /* الحواف المستديرة لأول وآخر صف */
            .details-table tr:first-child th:first-child {
                border-top-left-radius: 12px;
            }

            .details-table tr:first-child th:last-child {
                border-top-right-radius: 12px;
            }

            .details-table tr:last-child td:first-child {
                border-bottom-left-radius: 12px;
            }

            .details-table tr:last-child td:last-child {
                border-bottom-right-radius: 12px;
            }


        /* Responsive adjustments */
        @media (max-width: 768px) {
            .details-table {
                font-size: 14px;
            }

                .details-table td {
                    font-size: 12px;
                    padding: 8px;
                }
        }

        .full-table-container {
            border: 3px solid #007bff; /* إطار خارجي أزرق */
            padding: 20px; /* مسافة داخلية بين الإطار والمحتوى */
            border-radius: 15px; /* حواف مستديرة للإطار الخارجي */
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2); /* ظل الإطار الخارجي */
            margin-top: 20px; /* مسافة بين الإطار العلوي والمحتوى */
        }




        /* لون زر التوجيه التلقائي */
.auto-path-btn {
    background-color: #0056b3;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    margin: 5px;
    transition: all 0.3s ease;
}

.auto-path-btn:hover {
    background-color: #003d82;
}

.auto-path-active {
    background-color: #28a745;
}

.btn-group {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}
.supervisors-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin: 15px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.checkbox-container {
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.checkbox-container:hover {
    background-color: #e9ecef;
}

.action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.action-buttons .submit-button,
.action-buttons .reject-button,
.action-buttons .return-button {
    flex: 1;
}
.spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .auto-path-btn.disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }




    .rtl-alert {
    direction: rtl !important;
}

.rtl-text {
    text-align: right !important;
}

.swal2-popup {
    font-family: 'Arial', sans-serif !important;
}
/* تحسين مظهر التنبيهات */
.swal2-popup {
    font-size: 1.2em !important;
    border-radius: 10px !important;
}

.swal2-title {
    font-weight: bold !important;
}

.swal2-toast {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* أنماط خاصة لكل نوع تنبيه */
.swal2-success {
    background-color: #d4edda !important;
}

.swal2-warning {
    background-color: #fff3cd !important;
}

.swal2-error {
    background-color: #f8d7da !important;
}

.border-warning {
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}


.available-path {
    background-color: #28a745 !important; /* اللون الأخضر */
    color: white !important;
    position: relative;
    border: 2px solid #1c7430; /* إطار داكن حول الزر */
    box-shadow: 0px 0px 10px rgba(40, 167, 69, 0.5); /* ظل أخضر لزيادة الوضوح */
    font-weight: bold; /* زيادة سمك النص */
    transition: all 0.1s ease; /* تأثير انتقالي لإبراز التفعيل */
}

.available-path:hover {
    background-color: #218838; /* لون أغمق عند المرور فوق الزر */
    border-color: #1e7e34; /* تغيير لون الإطار عند التمرير */
    box-shadow: 0px 0px 15px rgba(40, 167, 69, 0.75); /* زيادة الظل عند التمرير */
}



.available-path::after {
    content: '✓';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5em;
}

.disabled-path {
    background-color: #6c757d !important;
    color: white !important;
    opacity: 0.65;
    cursor: not-allowed;
}

.alert {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert ul {
    margin-right: 1.5rem;
}

.alert li {
    margin: 5px 0;
}

.alert-info {
    background-color: #e9f7fd;
    border-right: 4px solid #007bff; /* لون أزرق */
}

.alert-secondary {
    background-color: #f8f9fa;
    border-right: 4px solid #6c757d;
}







/* تنسيق القسم الرئيسي */
.checkbox-section {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    direction: rtl;
}

/* تنسيق الجدول */
.checkbox-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تنسيق الخلايا */
.department-cell {
    padding: 12px 15px;
    border: 1px solid #dee2e6;
    background-color: #f8f9fa;
    width: 25%;
    text-align: right;
}

/* تنسيق حاوية مربع الاختيار */
.checkbox-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    transition: background-color 0.2s;
}

.checkbox-container:hover {
    background-color: #e9ecef;
    border-radius: 4px;
}

/* تنسيق النص */
.department-label {
    margin: 0;
    font-size: 14px;
    color: #495057;
    flex-grow: 1;
    padding-right: 10px;
    font-weight: 500;
}

/* تنسيق مربع الاختيار */
.checkbox-input {
    width: 18px !important;
    height: 18px !important;
    margin-left: 8px !important;
    cursor: pointer;
}

/* تنسيق الصفوف المتناوبة */
.checkbox-table tr:nth-child(even) .department-cell {
    background-color: #ffffff;
}

.checkbox-table tr:nth-child(odd) .department-cell {
    background-color: #f8f9fa;
}

/* تأثيرات إضافية */
.checkbox-container input[type="checkbox"]:checked + label {
    color: #0056b3;
    font-weight: bold;
}

/* تحسينات للطباعة */
@media print {
    .checkbox-section {
        padding: 0;
        box-shadow: none;
    }
    
    .checkbox-table {
        border: 1px solid #000;
    }
    
    .department-cell {
        border: 1px solid #000;
        padding: 8px;
    }
    
    .department-label {
        color: #000;
    }
}


/* أزرار التحديد */

.form-container {
    margin: 20px auto;
    text-align: center;
}

.dropdown-container {
    margin-bottom: 15px; /* مسافة أسفل القائمة المنسدلة */
}

.buttons-container {
    display: flex;
    flex-direction: column;
    gap: 15px; /* مسافة بين الصفوف */
}

.path-buttons, .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px; /* المسافة بين الأزرار */
    justify-content: center; /* محاذاة الأزرار في المنتصف */
}



/* أزرار المسارات */
.path-btn {
    background-color: #007bff; /* أزرق */
    color: white;
    border: 1px solid #0056b3;
}

.path-btn:hover {
    background-color: #0056b3; /* أزرق غامق */
}
/*  المعطلة أزرار المسارات */
.path-btn.disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    opacity: 0.6;
}






/* زر تحديد الكل */
.select-btn {
    background-color: #4CAF50; /* أخضر */
    color: white;
    border: 1px solid #388E3C;
}

.select-btn:hover {
    background-color: #45a049; /* أخضر غامق */
}

/* زر إلغاء الكل */
.select-btn.unselect {
    background-color: #f44336; /* أحمر */
    color: white;
    border: 1px solid #d32f2f;
}

.select-btn.unselect:hover {
    background-color: #e53935; /* أحمر غامق */
}


/* زر تحويل للمدير */
.btn-primary {
    background-color: #17a2b8; /* سماوي */
    color: white;
    border: 1px solid #117a8b;
}
.btn-primary:hover {
    background-color: #117a8b; /* سماوي غامق */
}
/* تحسين عرض الأزرار */
.btn {
    padding: 13px 20px;
    font-size: 16px;
    border-radius: 7px;
    border: none;
    cursor: pointer;
    min-width: 120px; /* حجم موحد للأزرار */
    text-align: center;
    transition: all 0.3s ease;
}

/* عند التمرير فوق الأزرار */
.btn:hover {
    opacity: 0.9;
}
/* تحسين ألوان مربعات النصوص */
.textbox1 {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}

.textbox1:focus {
    border-color: #80bdff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

        /* إضافة خلفية للصفحة */
        body {
            background-color: #f4f7fa; /* خلفية فاتحة للصفحة */
        }

        /* تنسيق زر إظهار/إخفاء */
.toggle-button {
    display: inline-block;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    background-color: #007bff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.toggle-button:hover {
    background-color: #0056b3;
    transform: scale(1.05);
}

.toggle-button:active {
    background-color: #004494;
    transform: scale(1);
}


  



    </style>

 <script type="text/javascript">
     // تأكيد التحويل العام
     function confirmSubmit() {
         return Swal.fire({
             title: 'تأكيد التحويل',
             text: 'هل أنت متأكد من تحويل الطلب؟',
             icon: 'question',
             showCancelButton: true,
             confirmButtonText: 'نعم، تحويل',
             cancelButtonText: 'إلغاء'
         }).then((result) => {
             if (result.isConfirmed) {
                 showLoading(); // إظهار مؤشر التحميل
             }
             return result.isConfirmed;
         });
     }

     // تأكيد التوجيه التلقائي
     function confirmAutoPath() {
         return Swal.fire({
             title: 'تأكيد التوجيه التلقائي',
             text: 'هل أنت متأكد من تطبيق التوجيه التلقائي؟',
             icon: 'question',
             showCancelButton: true,
             confirmButtonText: 'نعم',
             cancelButtonText: 'لا'
         }).then((result) => {
             if (result.isConfirmed) {
                 showLoading(); // إظهار مؤشر التحميل
             }
             return result.isConfirmed;
         });
     }

     // رسالة النجاح
     function showSuccess(message) {
         hideLoading(); // إخفاء مؤشر التحميل
         Swal.fire({
             title: 'تم بنجاح',
             text: message,
             icon: 'success',
             confirmButtonText: 'حسناً'
         });
     }

     // رسالة الخطأ
     function showError(message) {
         hideLoading(); // إخفاء مؤشر التحميل
         Swal.fire({
             title: 'خطأ',
             text: message,
             icon: 'error',
             confirmButtonText: 'حسناً'
         });
     }

     // إظهار مؤشر التحميل
     function showLoading() {
         document.getElementById('loadingIndicator').style.display = 'flex';
     }

     // إخفاء مؤشر التحميل
     function hideLoading() {
         document.getElementById('loadingIndicator').style.display = 'none';
     }

     // التحقق من اختيار طلب
     function validateSelection() {
         var ddl = document.getElementById('<%= ddlOrderNumbers.ClientID %>');
         if (ddl.value === '') {
             showError('الرجاء اختيار رقم الطلب أولاً');
             return false;
         }
         return true;
     }
     function confirmDirectToManager() {
         return Swal.fire({
             title: 'تأكيد التحويل المباشر',
             html: '<div class="text-right">' +
                 '<strong class="text-danger">تنبيه هام جداً:</strong><br>' +
                 'هذا الإجراء سيقوم بتحويل الطلب مباشرة إلى مدير الموارد البشرية،<br>' +
                 'متجاوزاً جميع المشرفين.<br><br>' +
                 'هل أنت متأكد من المتابعة؟</div>',
             icon: 'warning',
             showCancelButton: true,
             confirmButtonText: 'نعم، متابعة',
             cancelButtonText: 'إلغاء',
             customClass: {
                 title: 'text-right',
                 htmlContainer: 'text-right',
                 popup: 'swal2-rtl'
             }
         }).then((result) => {
             return result.isConfirmed;
         });
     }
 </script>



<div class="form-container">
    <h2 class="form-header">
        تحويل لاعتماد:
        <asp:Label ID="LabelMessage" runat="server" ForeColor="Green" Visible="False" Font-Size="Large" />
        <asp:Label ID="LabelError" runat="server" ForeColor="Red" Visible="False" Font-Size="Large" />
    </h2>

    <!-- القائمة المنسدلة -->
    <div class="dropdown-container">
        <asp:DropDownList ID="ddlOrderNumbers" runat="server" 
            CssClass="custom-dropdown" 
            Width="209px" 
            Font-Size="20px" 
            AutoPostBack="true" 
            OnSelectedIndexChanged="ddlOrderNumbers_SelectedIndexChanged">
            <asp:ListItem Text="-- اختر رقم الطلب --" Value="" />
        </asp:DropDownList>
    </div>

    <!-- الأزرار -->
    <div class="buttons-container">
        <!-- الصف الأول - أزرار المسار -->
<div class="path-buttons">
    <asp:Button ID="btnPath1" runat="server" Text="مسار1" CssClass="btn path-btn" OnClick="Path1_Click" />
    <asp:Button ID="btnPath2" runat="server" Text="مسار2" CssClass="btn path-btn" OnClick="Path2_Click" />
    <asp:Button ID="btnPath3" runat="server" Text="مسار3" CssClass="btn path-btn" OnClick="Path3_Click" />
    <asp:Button ID="btnPath4" runat="server" Text="مسار4" CssClass="btn path-btn" OnClick="Path4_Click" />
    <asp:Button ID="btnPath5" runat="server" Text="مسار5" CssClass="btn path-btn" OnClick="Path5_Click" />
    <asp:Button ID="btnPath6" runat="server" Text="مسار6" CssClass="btn path-btn" OnClick="Path6_Click" />
</div>


        <!-- الصف الثاني - أزرار التحديد والتوجيه -->
<div class="action-buttons">
    <asp:Button ID="btnAutoPath" runat="server" Text="التوجيه التلقائي" CssClass="btn auto-path-btn" OnClick="btnAutoPath_Click" OnClientClick="return validateSelection() && confirmAutoPath();" />
    <asp:Button ID="ButtonSelectAll" runat="server" Text="تحديد الكل" CssClass="btn select-btn" OnClick="ButtonSelectAll_Click" OnClientClick="return validateSelection();" />
    <asp:Button ID="ButtonUnSelectAll" runat="server" Text="إلغاء الكل" CssClass="btn select-btn unselect" OnClick="ButtonUnSelectAll_Click" OnClientClick="return validateSelection();" />
<asp:Button ID="btnDirectToManager" runat="server" 
    Text="تحويل للمدير" 
    CssClass="btn btn-danger" 
    OnClick="btnDirectToManager_Click" 
    OnClientClick="return confirm('تنبيه هام: سيتم تحويل الطلب مباشرة إلى مدير الموارد البشرية متجاوزاً مرحلة المشرفين. هل أنت متأكد من المتابعة؟');"
    ToolTip="تحويل الطلب مباشرة إلى مدير الموارد البشرية" />
</div>
    </div>





    <!-- مؤشر التحميل -->
    <div id="loadingIndicator" class="loading-overlay">
       <%-- <div class="spinner"></div>
        <div class="loading-text">جاري التحميل...</div>--%>
    </div>
</div>


    <!-- لوحة معلومات المسار التلقائي -->
<div id="AutoPathInfoPanel" runat="server" visible="false" class="mb-4">


    <!-- سيتم تعبئة المحتوى ديناميكيًا -->
</div>
    <div id="RejectionAlertPanel" runat="server" visible="false" class="mb-3"></div>
        <div id="checkboxContainer" runat="server" class="checkbox-section">
    <table class="checkbox-table">
        <!-- صف 1: المساعد الطبي والإدارات المرتبطة -->
        <tr class="header-row">
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk1" runat="server" Text="خدمات الموظفين" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk7" runat="server" Text="إدارة القانونية والالتزام" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk11" runat="server" Text="العيادات الخارجية" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk15" runat="server" Text="إدارة الأمن و السلامة" />
                </div>
            </td>
        </tr>
        
        <!-- صف 2: إدارة الموارد البشرية والإدارات المرتبطة -->
        <tr>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk2" runat="server" Text="إدارة تخطيط الموارد البشرية" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk6" runat="server" Text="إدارة الرواتب والاستحقاقات" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk10" runat="server" Text="قسم الملفات" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk14" runat="server" Text="إدارة تنمية الإيرادات" />
                </div>
            </td>
        </tr>
        
        <!-- صف 3: إدارة المعلومات والمخزون -->
        <tr>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk3" runat="server" Text="إدارة تقنية المعلومات" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk5" runat="server" Text="السجلات الطبية" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk9" runat="server" Text="إدارة الإسكان" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk13" runat="server" Text="وحدة مراقبة المخزون" />
                </div>
            </td>
        </tr>
        
        <!-- صف 4: الخدمات المساندة -->
        <tr>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk4" runat="server" Text="مراقبة الدوام" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk8" runat="server" Text="خدمات الموارد البشرية" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk12" runat="server" Text="التأمينات الاجتماعية" />
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <asp:CheckBox ID="chk16" runat="server" Text="الطب الاتصالي" />
                </div>
            </td>
        </tr>
    </table>
</div>


        <div id="txtContainer" runat="server" class="txt-container">
            <asp:TextBox ID="TextBoxRakam" runat="server" CssClass="textbox1" placeholder="التفاصيل والرقم" TextMode="MultiLine" Rows="3"></asp:TextBox>
            <asp:TextBox ID="txtActionRequired" runat="server" CssClass="textbox1" placeholder="الإجراءات المطلوبة"></asp:TextBox>
            <asp:TextBox ID="txtRejectReason" runat="server" CssClass="textbox1" placeholder="سبب الإلغاء/الإعادة" TextMode="MultiLine" Rows="2"></asp:TextBox>           
            <br />



            <br />
            <asp:Button ID="btnSubmit" runat="server" Text="تحويل الطلب" OnClick="btnSubmit_Click" CssClass="submit-button" />
            <asp:Button ID="btnNeedsAction" runat="server" Text="يتطلب إجراءات" OnClick="btnNeedsAction_Click" CssClass="action-button" />
            <asp:Button ID="btnRejectOrder" runat="server" Text="إعادة الطلب" OnClick="btnReturnOrder_Click" CssClass="return-button" />            
            <asp:Button ID="btnReject" runat="server" Text="إلغاء الطلب" OnClick="btnReject_Click" CssClass="reject-button" />
            

        </div>

        <br />
        <br />

<!-- زر التحكم بظهور القسم -->
<div class="admin-controls">
    <asp:Button ID="btnToggleRestoreSection" runat="server" Text="إظهار/إخفاء إدارة الطلبات المحولة للمشرفين" 
        OnClick="btnToggleRestoreSection_Click" CssClass="toggle-button" />
</div>

<asp:Panel ID="restoreSectionPanel" runat="server" CssClass="card border-primary shadow-lg mt-5" Visible="false">
    <div class="card-header bg-primary text-white py-3 position-sticky-top" style="z-index: 1; top: 0">
        <h4 class="card-title mb-0 fw-bold d-flex align-items-center">
            <i class="fas fa-people-arrows me-3 fa-beat-fade" style="--fa-beat-fade-opacity: 0.7"></i>
            إدارة الطلبات المحولة للمشرفين
        </h4>
    </div>
    
    <div class="card-body p-4">
        <!-- قسم البحث والتصفية -->
        <div class="row g-3 mb-4">
            <!-- حقل البحث -->
            <div class="col-12 col-lg-8">
                <div class="input-group input-group-lg has-validation">
                    <asp:TextBox ID="txtSearch" runat="server"
                        CssClass="form-control border-2 border-primary rounded-start-4 shadow-sm"
                        placeholder="ابحث برقم الطلب / اسم الموظف..."
                        AutoPostBack="true"
                        OnTextChanged="txtSearch_TextChanged">
                    </asp:TextBox>
                    <button class="input-group-text bg-primary text-white rounded-end-4 shadow-sm"
                            type="button"
                            style="width: 50px">
                        <i class="fas fa-search fa-fw"></i>
                    </button>
                </div>
            </div>

            <!-- أزرار التصفية -->
            <div class="col-12 col-lg-4">
                <div class="d-grid gap-2 d-lg-flex align-items-stretch">
                    <asp:LinkButton ID="btnFilterToday" runat="server"
                        CssClass="btn btn-outline-primary d-flex align-items-center justify-content-center flex-grow-1 rounded-3"
                        OnClick="btnFilter_Click"
                        CommandArgument="today">
                        <i class="fas fa-sun fa-fw me-2"></i>
                        <span class="small">اليوم</span>
                    </asp:LinkButton>
                    
                    <asp:LinkButton ID="btnFilterWeek" runat="server"
                        CssClass="btn btn-outline-primary d-flex align-items-center justify-content-center flex-grow-1 rounded-3"
                        OnClick="btnFilter_Click"
                        CommandArgument="week">
                        <i class="fas fa-calendar-week fa-fw me-2"></i>
                        <span class="small">أسبوع</span>
                    </asp:LinkButton>
                    
                    <asp:LinkButton ID="btnFilterMonth" runat="server"
                        CssClass="btn btn-outline-primary d-flex align-items-center justify-content-center flex-grow-1 rounded-3"
                        OnClick="btnFilter_Click"
                        CommandArgument="month">
                        <i class="fas fa-calendar-alt fa-fw me-2"></i>
                        <span class="small">شهر</span>
                    </asp:LinkButton>
                    
                    <asp:LinkButton ID="btnFilterAll" runat="server"
                        CssClass="btn btn-outline-primary d-flex align-items-center justify-content-center flex-grow-1 rounded-3"
                        OnClick="btnFilter_Click"
                        CommandArgument="all">
                        <i class="fas fa-infinity fa-fw me-2"></i>
                        <span class="small">الكل</span>
                    </asp:LinkButton>
                </div>
            </div>
        </div>

        <!-- محتوى القسم الرئيسي -->
        <div class="row g-4">
            <!-- القائمة المنسدلة -->
            <div class="col-12">
                <div class="form-floating position-relative">
                    <asp:DropDownList ID="ddlRestoreOrders" runat="server"
                        CssClass="form-select form-select-lg border-2 border-primary rounded-3 shadow-sm"
                        AutoPostBack="true"
                        OnSelectedIndexChanged="ddlRestoreOrders_SelectedIndexChanged">
                    </asp:DropDownList>
                    <label class="form-label text-primary fw-medium ps-4">
                        <i class="fas fa-layer-group me-2"></i>
                        اختر الطلب المراد استعادته
                    </label>
                </div>
            </div>

            <!-- تفاصيل الطلب -->
            <asp:Panel ID="restoreDetailsPanel" runat="server" Visible="false" CssClass="col-12 mt-4">
                <div class="alert alert-transparent border-3 border-primary rounded-4 shadow-lg">
                    <div class="row g-4">
                        <!-- الحالة الحالية -->
                        <div class="col-md-4">
                            <div class="card border-primary h-100">
                                <div class="card-body d-flex align-items-center">
                                    <i class="fas fa-info-circle fa-2x text-primary me-3"></i>
                                    <div>
                                        <div class="text-muted small">الحالة الحالية</div>
                                        <div class="h4 fw-bold text-primary mb-0">
                                            <asp:Label ID="lblRestoreCurrentStatus" runat="server" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تاريخ التحويل -->
                        <div class="col-md-4">
                            <div class="card border-primary h-100">
                                <div class="card-body d-flex align-items-center">
                                    <i class="fas fa-clock-rotate-left fa-2x text-primary me-3"></i>
                                    <div>
                                        <div class="text-muted small">تاريخ التحويل</div>
                                        <div class="h4 fw-bold text-primary mb-0">
                                            <asp:Label ID="lblTransferDate" runat="server" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- المشرفون -->
                        <div class="col-md-4">
                            <div class="card border-primary h-100">
                                <div class="card-body d-flex align-items-center">
                                    <i class="fas fa-user-shield fa-2x text-primary me-3"></i>
                                    <div>
                                        <div class="text-muted small">المشرفون المحول لهم</div>
                                        <div class="h4 fw-bold text-primary mb-0">
                                            <asp:Label ID="lblAssignedSupervisors" runat="server" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </asp:Panel>

            <!-- ملاحظات الاستعادة -->
            <div class="col-12 mt-4">
                <div class="form-floating position-relative">
                    <asp:TextBox ID="txtRestoreNotes" runat="server"
                        TextMode="MultiLine"
                        Rows="5"
                        CssClass="form-control border-2 border-primary rounded-3 shadow-sm"
                        placeholder="أدخل ملاحظاتك هنا..."
                        style="min-height: 150px; resize: none">
                    </asp:TextBox>
                    <label class="form-label text-primary fw-medium ps-4">
                        <i class="fas fa-comment-dots me-2"></i>
                        ملاحظات الاستعادة (اختياري)
                    </label>
                </div>
            </div>

            <!-- زر الاستعادة -->
            <div class="col-12 text-center mt-5">
                <asp:Button ID="btnRestoreOrder" runat="server"
                    Text="استعادة الطلب"
                    OnClick="btnRestoreOrder_Click"
                    CssClass="btn btn-lg btn-primary rounded-pill px-5 py-3 fw-bold shadow-lg"
                    style="transition: all 0.3s; background-image: linear-gradient(135deg, #0d6efd 0%, #4dabf7 100%)"
                    onmouseover="this.style.transform='scale(1.05)'; this.style.opacity='0.9'"
                    onmouseout="this.style.transform='none'; this.style.opacity='1'" />
            </div>
        </div>
    </div>

    <!-- نظام الرسائل -->
    <div class="mt-4 position-fixed bottom-0 end-0 m-3" style="z-index: 9999">
        <asp:Label ID="lblSuccess" runat="server" 
            CssClass="alert alert-success d-flex align-items-center shadow-lg rounded-pill"
            Visible="false"
            style="min-width: 300px">
            <i class="fas fa-check-circle fa-2x me-3"></i>
            <div class="fw-medium">تمت العملية بنجاح!</div>
        </asp:Label>
        
        <asp:Label ID="lblError" runat="server" 
            CssClass="alert alert-danger d-flex align-items-center shadow-lg rounded-pill"
            Visible="false"
            style="min-width: 300px">
            <i class="fas fa-times-circle fa-2x me-3"></i>
            <div class="fw-medium">حدث خطأ أثناء العملية</div>
        </asp:Label>
    </div>
</asp:Panel>


        <asp:Panel ID="OrderDetailsPanel" runat="server" Visible="false">



            <div class="full-table-container">
                <!-- Table 1 -->
                <table class="details-table">
                    <tr>
                        <th>رقم الطلب</th>
                        <td>
                            <asp:Label ID="LabelOrderNumber" runat="server"></asp:Label></td>
                        <th>تاريخ الطلب</th>
                        <td>
                            <asp:Label ID="LabelOrderDate" runat="server"></asp:Label></td>
                    </tr>
                    <tr>
                        <th>حالة الطلب</th>
                        <td colspan="3">
                            <asp:Label ID="LabelOrderStatus" runat="server"></asp:Label></td>
                    </tr>
                </table>

                                                                <!-- Table 2 -->
<table class="details-table">
    <tr>
        <th>نوع الطلب</th>
        <td colspan="3">
            <asp:Label ID="LabelOrderType" runat="server" />
        </td>
    </tr>
    <tr>
        <th>اسم الموظف</th>
        <td>
            <asp:Label ID="LabelEmployeeName" runat="server" />
        </td>
        <th>القسم</th>
        <td>
            <asp:Label ID="LabelDepartment" runat="server" />
        </td>
    </tr>
    <tr>
        <th>الوظيفة</th>
        <td>
            <asp:Label ID="LabelJobTitle" runat="server" />
        </td>
    </tr>
    <tr>
        <th>نوع التوظيف</th>
        <td>
            <asp:Label ID="LabelEmploymentType" runat="server" />
        </td>
        <th>المؤهل</th>
        <td>
            <asp:Label ID="LabelQualification" runat="server" />
        </td>
    </tr>
    <tr>
        <th>رقم الموظف</th>
        <td>
            <asp:Label ID="LabelEmployeeNumber" runat="server" />
        </td>
        <th>السجل المدني</th>
        <td>
            <asp:Label ID="LabelCivilRegistry" runat="server" />
        </td>
    </tr>
    <tr>
        <th>الجنسية</th>
        <td>
            <asp:Label ID="LabelNationality" runat="server" />
        </td>
        <th>رقم الجوال</th>
        <td>
            <asp:Label ID="LabelMobileNumber" runat="server" />
        </td>
    </tr>
   <tr>
        <td colspan="4">
            <strong>تفاصيل مقدم الطلب:</strong><br />
            <asp:Label ID="LabelNotes" runat="server" />
        </td>
    </tr>
</table>

                <!-- Table 3 -->
<table class="details-table">
    <tr>
        <th>تم التأكيد/الإلغاء من مدير القسم</th>
        <th>تم التأكيد/الإلغاء من قبل مساعد المدير</th>
        <th>تم التحويل/الإلغاء من قبل المنسق</th>
    </tr>
    <tr>
        <td>
            <asp:Label ID="LabelManagerApproval" runat="server" />
        </td>
        <td>
            <asp:Label ID="LabelSupervisorApproval" runat="server" />
        </td>
        <td>
            <asp:Label ID="LabelCoordinatorApproval" runat="server" />
        </td>
    </tr>
    <tr>
        <th>سبب الإلغاء/الإعادة</th>
        <th colspan="2">تفاصيل المنسق</th>
    </tr>
    <tr>
        <td>
            <asp:Label ID="LabelCancellationReason" runat="server" />
        </td>
        <td colspan="2">
            <asp:Label ID="LabelCoordinatorDetails" runat="server" />
        </td>
    </tr>
</table>

<asp:Button ID="ButtonDownload" runat="server" Text="تحميل مرفقات الطلب" OnClick="btnDownload_Click" CssClass="submit-button" />


                <!-- Table 4 -->
                <table class="details-table">
                    <tr>
                        <th>مشرف خدمات الموظفين</th>
                        <td>
                            <asp:Label ID="LabelMedicalServicesPermission" runat="server" /></td>
                        <th>مشرف إدارة تخطيط الموارد البشرية</th>
                        <td>
                            <asp:Label ID="LabelHRPlanningPermission" runat="server" /></td>
                    </tr>
                    <tr>
                        <th>مشرف إدارة تقنية المعلومات</th>
                        <td>
                            <asp:Label ID="LabelITPermission" runat="server" /></td>
                        <th>مشرف مراقبة الدوام</th>
                        <td>
                            <asp:Label ID="LabelAttendanceControlPermission" runat="server" /></td>
                    </tr>
                    <tr>
                        <th>مشرف السجلات الطبية</th>
                        <td>
                            <asp:Label ID="LabelMedicalRecordsPermission" runat="server" /></td>
                        <th>مشرف إدارة الرواتب والاستحقاقات</th>
                        <td>
                            <asp:Label ID="LabelPayrollPermission" runat="server" /></td>
                    </tr>
                    <tr>
                        <th>مشرف إدارة القانونية والالتزام</th>
                        <td>
                            <asp:Label ID="LabelLegalCompliancePermission" runat="server" /></td>
                        <th>مشرف خدمات الموارد البشرية</th>
                        <td>
                            <asp:Label ID="LabelHRServicesPermission" runat="server" /></td>
                    </tr>
                    <tr>
                        <th>مشرف إدارة الإسكان</th>
                        <td>
                            <asp:Label ID="LabelHousingPermission" runat="server" /></td>
                        <th>مشرف قسم الملفات</th>
                        <td>
                            <asp:Label ID="LabelFilesSectionPermission" runat="server" /></td>
                    </tr>
                    <tr>
                        <th>مشرف العيادات الخارجية</th>
                        <td>
                            <asp:Label ID="LabelOutpatientPermission" runat="server" /></td>
                        <th>مشرف التأمينات الاجتماعية</th>
                        <td>
                            <asp:Label ID="LabelSocialInsurancePermission" runat="server" /></td>
                    </tr>
                    <tr>
                        <th>مشرف وحدة مراقبة المخزون</th>
                        <td>
                            <asp:Label ID="LabelInventoryControlPermission" runat="server" /></td>
                        <th>مشرف إدارة تنمية الإيرادات</th>
                        <td>
                            <asp:Label ID="LabelSelfResourcesPermission" runat="server" /></td>
                    </tr>
                    <tr>
                        <th>مشرف إدارة الأمن و السلامة</th>
                        <td>
                            <asp:Label ID="LabelNursingPermission" runat="server" /></td>
                        <th>مشرف الطب الاتصالي</th>
                        <td>
                            <asp:Label ID="LabelEmployeeServicesPermission" runat="server" /></td>
                    </tr>





                </table>
                                <!-- Table 5 -->
<table class="details-table">
    <tr>
        <th>مدير الموارد البشرية</th>
        <td colspan="3">
            <asp:Label ID="LabelHRManagerApproval" runat="server" />
        </td>
    </tr>
</table>
            </div>




        </asp:Panel>

     
    
</asp:Content>
